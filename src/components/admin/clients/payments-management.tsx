'use client'

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  CreditCardIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  PencilIcon,
  ArrowDownTrayIcon,
  TrashIcon,
  PlusIcon,
  FunnelIcon,
  ListBulletIcon,
  Squares2X2Icon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'
import { ConfirmationModal } from '../shared/confirmation-modal'
import { ResponsivePagination } from '../shared/responsive-pagination'

// Types
interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
}

interface Invoice {
  id: string | number
  clientId: string | number
  projectId?: string | number
  totalAmount: number
  status: string
  dueDate: string
  description?: string
  _count?: {
    payments: number
  }
}

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  invoiceId: string | number
  createdAt: string
  updatedAt?: string
  currency?: string
  transactionId?: string
  processingFee?: number
  netAmount?: number
  refundedAmount?: number
  stripePaymentIntentId?: string
  paypalOrderId?: string
  promoCode?: string
  discountAmount?: number
  billingAddress?: any
  receiptEmail?: string
  metadata?: any
}

interface PaymentsManagementProps {
  client: Client
  project: Project
  invoice: Invoice
  selectedPayment: Payment | null
  onPaymentSelect: (payment: Payment | null) => void
  activeSection: string
  showSuccess: (title: string, message: string) => void
  showError: (title: string, message: string) => void
  showLoading: (title: string, message: string) => string
  showInfo: (title: string, message: string) => void
  clearLoadingNotifications: () => void
}

type ViewMode = 'list' | 'grid' | 'card'
type DisplayDensity = 'compact' | 'comfortable' | 'spacious'

// PaymentRow Component
interface PaymentRowProps {
  payment: Payment
  isSelected: boolean
  onSelect: () => void
  onAction: (action: string) => void
  onPaymentSelect: (payment: Payment | null) => void
  selectedPayment: Payment | null
  activeSection: string
  visibleColumns: string[]
  displayDensity: DisplayDensity
  viewMode: ViewMode
  actionLoading: string | null
  formatDate: (date: string) => string
  formatCurrency: (amount: number, currency?: string) => string
  getStatusColor: (status: string) => string
  getStatusIcon: (status: string) => React.ReactNode
  getPaymentMethodIcon: (method: string) => React.ReactNode
}

const PaymentRow = React.memo(({
  payment,
  isSelected,
  onSelect,
  onAction,
  onPaymentSelect,
  selectedPayment,
  activeSection,
  visibleColumns,
  displayDensity,
  viewMode,
  actionLoading,
  formatDate,
  formatCurrency,
  getStatusColor,
  getStatusIcon,
  getPaymentMethodIcon
}: PaymentRowProps) => {
  const isCompact = displayDensity === 'compact'
  const isSpacious = displayDensity === 'spacious'
  const isCurrentPayment = selectedPayment?.id === payment.id && activeSection === 'payments'
  
  // Get padding class based on density
  const getPaddingClass = () => {
    switch (displayDensity) {
      case 'compact':
        return 'py-1.5'
      case 'spacious':
        return 'py-4.5'
      default:
        return 'py-3'
    }
  }

  // List View (Table Row)
  if (viewMode === 'list') {
    return (
      <tr
        className={`cursor-pointer transition-colors ${
          isCurrentPayment ? 'bg-purple-50' : ''
        } ${
          isSelected ? 'bg-purple-50 border-l-4 border-purple-500' : ''
        } ${getPaddingClass()}`}
        onClick={() => onPaymentSelect(payment)}
      >
        <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => {
              e.stopPropagation()
              onSelect()
            }}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </td>
        
        {visibleColumns.includes('amount') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm font-medium text-gray-500">
              {formatCurrency(payment.amount, payment.currency)}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('status') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
              {getStatusIcon(payment.status)}
              <span className="ml-1">{payment.status}</span>
            </span>
          </td>
        )}
        
        {visibleColumns.includes('paymentDate') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">{formatDate(payment.paymentDate)}</div>
          </td>
        )}
        
        {visibleColumns.includes('paymentMethod') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="flex items-center">
              {getPaymentMethodIcon(payment.paymentMethod)}
              <span className="ml-2 text-sm text-gray-500">{payment.paymentMethod}</span>
            </div>
          </td>
        )}
        
        {visibleColumns.includes('notes') && (
          <td className={`px-6 ${getPaddingClass()}`}>
            <div className="text-sm text-gray-500 max-w-xs truncate">
              {payment.notes || '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('currency') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">
              {payment.currency || 'USD'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('transactionId') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm text-gray-500 font-mono">
              {payment.transactionId ? payment.transactionId.substring(0, 12) + '...' : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('processingFee') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">
              {payment.processingFee ? formatCurrency(payment.processingFee, payment.currency) : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('netAmount') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">
              {payment.netAmount ? formatCurrency(payment.netAmount, payment.currency) : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('createdAt') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">{formatDate(payment.createdAt)}</div>
          </td>
        )}
        
        {visibleColumns.includes('updatedAt') && (
          <td className={`px-6 ${getPaddingClass()} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">
              {payment.updatedAt ? formatDate(payment.updatedAt) : '-'}
            </div>
          </td>
        )}
        
        <td className={`px-6 ${getPaddingClass()} whitespace-nowrap text-right text-sm font-medium`}>
          <div className="flex items-center justify-end space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
              disabled={actionLoading === `edit-${payment.id}`}
              className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit Payment"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('download')
              }}
              className="p-1.5 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download Receipt"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>
            {payment.status === 'completed' && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onAction('refund')
                }}
                className="p-1.5 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-md transition-colors"
                title="Refund Payment"
              >
                <ArrowPathIcon className="h-4 w-4" />
              </button>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
              disabled={actionLoading === `delete-${payment.id}`}
              className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete Payment"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    )
  }

  // Grid View (Compact Card)
  if (viewMode === 'grid') {
    return (
      <motion.div
        onClick={() => onPaymentSelect(payment)}
        className={`group relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md overflow-hidden ${
          isSelected 
            ? 'bg-purple-50 border-l-4 border-purple-500' 
            : isCurrentPayment
            ? 'ring-2 ring-purple-500 ring-offset-2 bg-purple-50/50 border-purple-300'
            : 'bg-white hover:bg-gray-50/50 hover:border-gray-300'
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onMouseEnter={(e) => {
          // Show/hide action menu on hover (desktop only)
          if (window.innerWidth > 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '1';
              actionMenu.style.transform = 'translateX(0)';
              actionMenu.style.pointerEvents = 'auto';
            }
          }
        }}
        onMouseLeave={(e) => {
          // Hide action menu when mouse leaves (desktop only)
          if (window.innerWidth > 1024) {
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '0';
              actionMenu.style.transform = 'translateX(100%)';
              actionMenu.style.pointerEvents = 'none';
            }
          }
        }}
        onTouchStart={(e) => {
          // Show action menu on touch (mobile and small screens)
          if (window.innerWidth <= 1024) {
            e.preventDefault();
            const actionMenu = e.currentTarget.querySelector('.action-menu') as HTMLElement;
            if (actionMenu) {
              actionMenu.style.opacity = '1';
              actionMenu.style.transform = 'translateX(0)';
              actionMenu.style.pointerEvents = 'auto';
            }
          }
        }}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation()
                onSelect()
              }}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div className="text-lg font-semibold text-gray-500">
              {formatCurrency(payment.amount, payment.currency)}
            </div>
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
            {getStatusIcon(payment.status)}
            <span className="ml-1">{payment.status}</span>
          </span>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-center text-gray-600">
            <CalendarIcon className="h-4 w-4 mr-2" />
            <span>{formatDate(payment.paymentDate)}</span>
          </div>
          
          <div className="flex items-center text-gray-600">
            {getPaymentMethodIcon(payment.paymentMethod)}
            <span className="ml-2">{payment.paymentMethod}</span>
          </div>
          
          {payment.notes && (
            <div className="text-gray-600 line-clamp-2">
              {payment.notes}
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200">
          <div className="text-xs text-gray-500">
            {formatDate(payment.createdAt)}
          </div>
          
          {/* Mobile Action Button - Always visible on mobile and small screens */}
          <div className="lg:hidden">
            <button
              onClick={(e) => {
                e.stopPropagation()
                const actionMenu = e.currentTarget.closest('.group')?.querySelector('.action-menu') as HTMLElement;
                if (actionMenu) {
                  const isVisible = actionMenu.style.opacity === '1';
                  actionMenu.style.opacity = isVisible ? '0' : '1';
                  actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';
                  actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';
                }
              }}
              className="p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              title="Show Actions"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
          </div>
        </div>

        {/* Actions Sidebar - Professional Overlay */}
        <div className={`action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 ${
          displayDensity === 'compact' 
            ? 'top-2 right-2 bottom-2 w-10 space-y-2' 
            : displayDensity === 'spacious' 
            ? 'top-4 right-4 bottom-4 w-14 space-y-6' 
            : 'top-3 right-3 bottom-3 w-12 space-y-4'
        }`} style={{
          opacity: '0',
          transform: 'translateX(100%)',
          pointerEvents: 'none'
        }}>
          {/* Edit Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              onAction('edit')
            }}
            className={`group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              displayDensity === 'compact' ? 'w-6 h-6' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            }`}
            title="Edit Payment"
          >
            <PencilIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
          </button>
          
          {/* Download Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              onAction('download')
            }}
            className={`group/btn relative inline-flex items-center justify-center bg-green-600 hover:bg-green-700 border border-green-500 hover:border-green-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              displayDensity === 'compact' ? 'w-6 h-6' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            }`}
            title="Download Payment"
          >
            <ArrowDownTrayIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
          </button>
          
          {/* Refund Button - Only show for completed payments */}
          {payment.status === 'completed' && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('refund')
              }}
              className={`group/btn relative inline-flex items-center justify-center bg-orange-600 hover:bg-orange-700 border border-orange-500 hover:border-orange-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
                displayDensity === 'compact' ? 'w-6 h-6' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
              }`}
              title="Refund Payment"
            >
              <ArrowPathIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
            </button>
          )}
          
          {/* Delete Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              onAction('delete')
            }}
            className={`group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
              displayDensity === 'compact' ? 'w-6 h-6' : displayDensity === 'spacious' ? 'w-10 h-10' : 'w-8 h-8'
            }`}
            title="Delete Payment"
          >
            <TrashIcon className={`group-hover/btn:scale-110 ${displayDensity === 'compact' ? 'h-3 w-3' : displayDensity === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'}`} />
          </button>
        </div>
      </motion.div>
    )
  }

  // Card View (Detailed Card)
  return (
    <motion.div
      onClick={() => onPaymentSelect(payment)}
      className={`border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isSelected 
          ? 'bg-purple-50 border-l-4 border-purple-500' 
          : isCurrentPayment
          ? 'ring-2 ring-purple-500 ring-offset-2 bg-purple-50/50 border-purple-300'
          : 'bg-white hover:bg-gray-50/50 hover:border-gray-300'
      }`}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-3 mb-4">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation()
                onSelect()
              }}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <h3 className="text-lg font-medium text-gray-500">
              {formatCurrency(payment.amount, payment.currency)}
            </h3>

            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
              {getStatusIcon(payment.status)}
              <span className="ml-1">{payment.status}</span>
            </span>
          </div>

          {payment.notes && (
            <p className="mt-2 text-sm text-gray-600 line-clamp-2">
              {payment.notes}
            </p>
          )}

          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center text-gray-600">
              <CalendarIcon className="h-4 w-4 mr-2 text-blue-600" />
              <span>{formatDate(payment.paymentDate)}</span>
            </div>

            <div className="flex items-center text-gray-600">
              {getPaymentMethodIcon(payment.paymentMethod)}
              <span className="ml-2">{payment.paymentMethod}</span>
            </div>

            {payment.currency && payment.currency !== 'USD' && (
              <div className="flex items-center text-gray-600">
                <CurrencyDollarIcon className="h-4 w-4 mr-2 text-green-600" />
                <span>{payment.currency}</span>
              </div>
            )}

            {payment.transactionId && (
              <div className="flex items-center text-gray-600">
                <DocumentTextIcon className="h-4 w-4 mr-2 text-purple-600" />
                <span className="font-mono text-xs">{payment.transactionId.substring(0, 16)}...</span>
              </div>
            )}

            {payment.processingFee && payment.processingFee > 0 && (
              <div className="flex items-center text-gray-600">
                <span className="text-xs">Fee: {formatCurrency(payment.processingFee, payment.currency)}</span>
              </div>
            )}

            {payment.netAmount && (
              <div className="flex items-center text-gray-600">
                <span className="text-xs">Net: {formatCurrency(payment.netAmount, payment.currency)}</span>
              </div>
            )}
          </div>

          {(payment.promoCode || payment.discountAmount || payment.refundedAmount) && (
            <div className="mt-4 flex flex-wrap items-center gap-4 text-sm text-gray-500">
              {payment.promoCode && (
                <div>
                  <span className="font-medium">Promo:</span> {payment.promoCode}
                </div>
              )}
              {payment.discountAmount && payment.discountAmount > 0 && (
                <div>
                  <span className="font-medium">Discount:</span> {formatCurrency(payment.discountAmount, payment.currency)}
                </div>
              )}
              {payment.refundedAmount && payment.refundedAmount > 0 && (
                <div>
                  <span className="font-medium">Refunded:</span> {formatCurrency(payment.refundedAmount, payment.currency)}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="ml-6 text-right">
          <div className="text-2xl font-bold text-gray-500">
            {formatCurrency(payment.amount, payment.currency)}
          </div>
          {payment.netAmount && payment.netAmount !== payment.amount && (
            <div className="text-sm text-gray-500">
              Net: {formatCurrency(payment.netAmount, payment.currency)}
            </div>
          )}

          <div className="flex items-center justify-end space-x-2 mt-4">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
              disabled={actionLoading === `edit-${payment.id}`}
              className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit Payment"
            >
              <PencilIcon className="h-4 w-4" />
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('download')
              }}
              className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download Receipt"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>

            {payment.status === 'completed' && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onAction('refund')
                }}
                className="p-2 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-md transition-colors"
                title="Refund Payment"
              >
                <ArrowPathIcon className="h-4 w-4" />
              </button>
            )}

            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
              disabled={actionLoading === `delete-${payment.id}`}
              className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete Payment"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      <div className="mt-6 border-t border-gray-200 pt-4">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div>
            Created {formatDate(payment.createdAt)}
          </div>
          {payment.updatedAt && (
            <div>
              Updated {formatDate(payment.updatedAt)}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
})

// Main Component
export function PaymentsManagement({ client, project, invoice, selectedPayment, onPaymentSelect, activeSection, showSuccess, showError, showLoading, showInfo, clearLoadingNotifications }: PaymentsManagementProps) {
  // State management
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  
  // Enhanced controls state
  const [selectedPayments, setSelectedPayments] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [displayDensity, setDisplayDensity] = useState<DisplayDensity>('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'amount', 'status', 'paymentDate', 'paymentMethod', 'notes', 'updatedAt'
  ])
  const [sortBy, setSortBy] = useState('paymentDate')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnControls, setShowColumnControls] = useState(false)
  const [showDensityControls, setShowDensityControls] = useState(false)
  const [gridColumns, setGridColumns] = useState(3)
  
  // Dropdown space calculation for proper layout
  const [dropdownSpaceNeeded, setDropdownSpaceNeeded] = useState(0)
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  
  // Modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null)
  const [paymentToDelete, setPaymentToDelete] = useState<Payment | null>(null)
  
  // Confirmation modal state
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean
    title: string
    message: string
    details?: string
    type: 'danger' | 'warning' | 'info' | 'success' | 'verification'
    onConfirm: () => void
    onCancel: () => void
    verificationData?: {
      canDelete: boolean
      reason?: string
      dependencies?: string[]
    }
    showVerification?: boolean
  }>({
    isOpen: false,
    title: '',
    message: '',
    type: 'danger',
    onConfirm: () => {},
    onCancel: () => {}
  })
  
  // Refs
  const searchInputRef = useRef<HTMLInputElement>(null)
  
  // Notifications - using props from parent component

  // Available columns configuration
  const availableColumns = useMemo(() => [
    { key: 'amount', label: 'Amount', sortable: true },
    { key: 'status', label: 'Status', sortable: true },
    { key: 'paymentDate', label: 'Payment Date', sortable: true },
    { key: 'paymentMethod', label: 'Payment Method', sortable: true },
    { key: 'notes', label: 'Notes', sortable: false },
    { key: 'currency', label: 'Currency', sortable: true },
    { key: 'transactionId', label: 'Transaction ID', sortable: false },
    { key: 'processingFee', label: 'Processing Fee', sortable: true },
    { key: 'netAmount', label: 'Net Amount', sortable: true },
    { key: 'createdAt', label: 'Created', sortable: true },
    { key: 'updatedAt', label: 'Updated', sortable: true }
  ], [])

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch payments when client, project, invoice, or search/sort/filters change
  useEffect(() => {
    if (client && project && invoice) {
      fetchPayments()
    }
  }, [client, project, invoice, debouncedSearchQuery, sortBy, sortOrder, filters, currentPage, pageSize])

  // Utility functions
  const fetchPayments = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      showInfo('Loading Payments', 'Retrieving payment data...')

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sortBy,
        sortOrder,
      })

      if (debouncedSearchQuery) {
        params.append('search', debouncedSearchQuery)
      }

      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value)
        }
      })

      const response = await fetch(`/api/admin/invoices-new/${invoice.id}/payments?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch payments: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        const paymentsData = data.data || []
        setPayments(paymentsData)
        setError(null)
        showSuccess('Payments Loaded', `Loaded ${paymentsData.length} payment${paymentsData.length === 1 ? '' : 's'}`)
      } else {
        throw new Error(data.error || 'Failed to fetch payments')
      }
    } catch (err) {
      console.error('Error fetching payments:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setPayments([])
      showError('Failed to Load Payments', 'Unable to retrieve payments')
    } finally {
      setLoading(false)
    }
  }, [client, project, invoice, debouncedSearchQuery, sortBy, sortOrder, filters, currentPage, pageSize, showInfo, showSuccess, showError])

  const handleCreate = useCallback(async (data: any) => {
    try {
      setActionLoading('create')
      const response = await fetch(`/api/admin/invoices-new/${invoice.id}/payments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error('Failed to create payment')
      }

      await fetchPayments()
      setIsCreateModalOpen(false)
    } catch (error) {
      console.error('Error creating payment:', error)
      throw error
    } finally {
      setActionLoading(null)
    }
  }, [invoice.id, fetchPayments])

  const handleUpdate = useCallback(async (id: string, data: any) => {
    try {
      setActionLoading(`edit-${id}`)
      const response = await fetch(`/api/admin/payments/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error('Failed to update payment')
      }

      await fetchPayments()
      setIsEditModalOpen(false)
      setEditingPayment(null)
    } catch (error) {
      console.error('Error updating payment:', error)
      throw error
    } finally {
      setActionLoading(null)
    }
  }, [fetchPayments])

  const handleDelete = useCallback(async (id: string) => {
    try {
      setActionLoading(`delete-${id}`)
      const response = await fetch(`/api/admin/payments/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete payment')
      }

      await fetchPayments()
      if (selectedPayment?.id === id) {
        onPaymentSelect(null)
      }
      showSuccess('Payment Deleted', 'Payment deleted successfully')
    } catch (error) {
      console.error('Error deleting payment:', error)
    } finally {
      setActionLoading(null)
    }
  }, [fetchPayments, selectedPayment, onPaymentSelect])

  const checkPaymentDependencies = useCallback(async (payment: Payment) => {
    try {
      showLoading('Verifying Payment', `Checking payment ${payment.id}...`)
      
      // Payments typically don't have dependencies, but we can check for any related records
      // For now, payments are generally safe to delete
      const result = {
        canDelete: true,
        reason: 'No dependencies found - safe to delete',
        dependencies: []
      }
      
      return result
    } catch (error) {
      console.error('Error checking payment dependencies:', error)
      return {
        canDelete: false,
        reason: 'Error checking dependencies',
        dependencies: ['Unable to verify dependencies']
      }
    }
  }, [])

  const showDeleteConfirmation = useCallback(async (payment: Payment) => {
    // Set the payment to delete
    setPaymentToDelete(payment)

    // Check dependencies first
    const verificationData = await checkPaymentDependencies(payment)
    
    setConfirmationModal({
      isOpen: true,
      title: '',
      message: '',
      type: verificationData.canDelete ? 'danger' : 'verification',
      verificationData,
      showVerification: true,
      onConfirm: () => {
        setConfirmationModal(prev => ({ ...prev, isOpen: false }))
        setPaymentToDelete(null)
        if (verificationData.canDelete) {
          handleDelete(String(payment.id))
        }
      },
      onCancel: () => {
        setConfirmationModal(prev => ({ ...prev, isOpen: false }))
        setPaymentToDelete(null)
        // Clear any remaining loading notifications when modal is cancelled
        clearLoadingNotifications()
      }
    })
  }, [handleDelete, checkPaymentDependencies])


  const handleAction = useCallback((action: string, payment: Payment) => {
    console.log('handleAction called with:', { action, paymentId: payment.id })
    switch (action) {
      case 'edit':
        setEditingPayment(payment)
        setIsEditModalOpen(true)
        showInfo('Edit Payment', `Editing payment: $${payment.amount}`)
        break
      case 'delete':
        showDeleteConfirmation(payment)
        break
      case 'refund':
        console.log('Refund payment:', payment.id)
        break
      case 'download':
        console.log('Download receipt:', payment.id)
        break
      default:
        break
    }
  }, [showDeleteConfirmation])

  const handleBulkAction = useCallback(async (action: string, paymentIds: string[]) => {
    try {
      setActionLoading(`bulk-${action}`)
      
      // Handle different bulk actions
      if (action === 'refund') {
        // For refund, we'll mark payments as refunded
        for (const paymentId of paymentIds) {
          const response = await fetch(`/api/admin/payments/${paymentId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              status: 'refunded',
              notes: 'Bulk refund action'
            })
          })

          if (!response.ok) {
            throw new Error(`Failed to refund payment ${paymentId}`)
          }
        }
      } else if (action === 'download') {
        // For download, we'll create a download link for each payment
        // This is a placeholder - you might want to implement actual download logic
        console.log('Downloading payments:', paymentIds)
        // Download notification handled by parent component
      } else if (action === 'delete') {
        // For delete, we'll delete each payment
        for (const paymentId of paymentIds) {
          const response = await fetch(`/api/admin/payments/${paymentId}`, {
            method: 'DELETE'
          })

          if (!response.ok) {
            throw new Error(`Failed to delete payment ${paymentId}`)
          }
        }
      }

      await fetchPayments()
      setSelectedPayments([])
    } catch (error) {
      console.error(`Error ${action} payments:`, error)
      showError('Bulk Action Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setActionLoading(null)
    }
  }, [fetchPayments])

  const showBulkDeleteConfirmation = useCallback((paymentIds: string[]) => {
    const count = paymentIds.length
    setConfirmationModal({
      isOpen: true,
      title: 'Delete Multiple Payments',
      message: `Are you sure you want to delete ${count} payment${count !== 1 ? 's' : ''}?`,
      details: 'This action cannot be undone and will permanently remove all selected payment records.',
      type: 'danger',
      onConfirm: () => {
        setConfirmationModal(prev => ({ ...prev, isOpen: false }))
        handleBulkAction('delete', paymentIds)
      },
      onCancel: () => {
        setConfirmationModal(prev => ({ ...prev, isOpen: false }))
        // Clear any remaining loading notifications when modal is cancelled
        clearLoadingNotifications()
      }
    })
  }, [handleBulkAction])

  const handleSelectPayment = useCallback((paymentId: string) => {
    setSelectedPayments(prev => {
      const newSelection = prev.includes(paymentId)
        ? prev.filter(id => id !== paymentId)
        : [...prev, paymentId]
      
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }, [])

  const handleClearSelection = useCallback(() => {
    setSelectedPayments([])
    setShowBulkActions(false)
  }, [])

  // Pagination logic
  const filteredPayments = useMemo(() => {
    let filtered = payments

    // Apply search filter
    if (debouncedSearchQuery) {
      const query = debouncedSearchQuery.toLowerCase()
      filtered = filtered.filter(payment =>
        payment.amount.toString().includes(query) ||
        payment.status.toLowerCase().includes(query) ||
        payment.paymentMethod.toLowerCase().includes(query) ||
        payment.transactionId?.toLowerCase().includes(query)
      )
    }

    // Apply status filter
    if (filters.status) {
      filtered = filtered.filter(payment => payment.status === filters.status)
    }

    // Apply payment method filter
    if (filters.paymentMethod) {
      filtered = filtered.filter(payment => payment.paymentMethod === filters.paymentMethod)
    }

    return filtered
  }, [payments, debouncedSearchQuery, filters])

  // Pagination calculations
  const totalPages = Math.max(1, Math.ceil(filteredPayments.length / pageSize))
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedPayments = filteredPayments.slice(startIndex, endIndex)

  const handleSelectAll = useCallback(() => {
    setSelectedPayments(prev => {
      const newSelection = prev.length === paginatedPayments.length 
        ? [] 
        : paginatedPayments.map(payment => String(payment.id))
      
      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }, [paginatedPayments])

  const handleSort = useCallback((column: string) => {
    setSortBy(column)
    setSortOrder(prev => sortBy === column && prev === 'asc' ? 'desc' : 'asc')
  }, [sortBy])

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }, [totalPages])

  const handleItemsPerPageChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize)
    setCurrentPage(1) // Reset to first page when changing page size
  }, [])

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchQuery, filters])

  // Calculate dropdown space needed for proper layout
  useEffect(() => {
    let spaceNeeded = 0
    
    if (showColumnControls) {
      spaceNeeded = Math.max(spaceNeeded, 200) // Column selector dropdown height
    }
    
    if (showDensityControls) {
      spaceNeeded = Math.max(spaceNeeded, 120) // Density dropdown height
    }
    
    if (showFilters) {
      spaceNeeded = Math.max(spaceNeeded, 300) // Filters dropdown height
    }
    
    setDropdownSpaceNeeded(spaceNeeded)
  }, [showColumnControls, showDensityControls, showFilters])

  // Utility functions for formatting and status
  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }, [])

  const formatCurrency = useCallback((amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount)
  }, [])

  const getStatusColor = useCallback((status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'refunded':
        return 'bg-blue-100 text-blue-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }, [])

  const getStatusIcon = useCallback((status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <CheckCircleIcon className="h-3 w-3" />
      case 'pending':
        return <ClockIcon className="h-3 w-3" />
      case 'failed':
        return <XCircleIcon className="h-3 w-3" />
      case 'refunded':
        return <ArrowPathIcon className="h-3 w-3" />
      case 'cancelled':
        return <XCircleIcon className="h-3 w-3" />
      default:
        return <ClockIcon className="h-3 w-3" />
    }
  }, [])

  const getPaymentMethodIcon = useCallback((method: string) => {
    switch (method?.toLowerCase()) {
      case 'card':
      case 'credit_card':
      case 'stripe':
        return <CreditCardIcon className="h-4 w-4" />
      case 'paypal':
        return <CurrencyDollarIcon className="h-4 w-4" />
      case 'bank_transfer':
        return <DocumentTextIcon className="h-4 w-4" />
      default:
        return <CreditCardIcon className="h-4 w-4" />
    }
  }, [])

  // Memoized stats data
  const statsData = useMemo(() => {
    const totalPayments = filteredPayments.length
    const completedPayments = filteredPayments.filter(p => p.status === 'completed').length
    const pendingPayments = filteredPayments.filter(p => p.status === 'pending').length
    const totalAmount = filteredPayments.reduce((sum, p) => sum + p.amount, 0)
    
    return [
      { label: 'Total Payments', value: totalPayments, color: 'text-purple-600', icon: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z' },
      { label: 'Completed', value: completedPayments, color: 'text-green-600', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' },
      { label: 'Pending', value: pendingPayments, color: 'text-yellow-600', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' },
      { label: 'Total Amount', value: totalAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0, maximumFractionDigits: 0 }), color: 'text-purple-600', icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1' }
    ]
  }, [filteredPayments])

  // Loading state
  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Payments</h3>
          <p className="text-sm text-red-700 mb-4">{error}</p>
          <button
            onClick={() => fetchPayments()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm" data-section="payments" style={{ paddingBottom: dropdownSpaceNeeded > 0 ? `${dropdownSpaceNeeded}px` : '0' }}>
        {/* Header Controls - Responsive Design */}
        <div className="space-y-3 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
          {/* Mobile Layout - Stacked */}
          <div className="flex flex-col space-y-3 lg:hidden">
            {/* Search Bar - Full Width on Mobile */}
            <div className="w-full">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search payments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
              </div>
            </div>

            {/* Mobile Controls - Single Row */}
            <div className="flex items-center gap-1">
              {/* View Mode Toggle - Stretched */}
              <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                    viewMode === 'list'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="List view"
                >
                  <ListBulletIcon className="h-3 w-3" />
                  <span className="text-xs font-medium hidden xs:inline">List</span>
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                    viewMode === 'grid'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="Grid view"
                >
                  <Squares2X2Icon className="h-3 w-3" />
                  <span className="text-xs font-medium hidden xs:inline">Grid</span>
                </button>
              </div>

              {/* Grid Columns Control (for grid view) */}
              {viewMode === 'grid' && (
                <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
                  <span className="text-xs font-medium text-gray-700 px-1">Col:</span>
                  <div className="flex items-center gap-0.5 flex-1">
                    {[1, 2, 3, 4].map((num) => (
                      <button
                        key={num}
                        onClick={() => setGridColumns(num)}
                        className={`flex-1 px-1.5 py-1 rounded text-xs font-medium ${
                          gridColumns === num
                            ? 'bg-white text-green-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        title={`${num} column${num > 1 ? 's' : ''}`}
                      >
                        {num}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Table Columns Control (for list view) */}
              {viewMode === 'list' && (
                <div className="relative flex-1">
                  <button
                    onClick={() => setShowColumnControls(!showColumnControls)}
                    className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                    title="Columns"
                  >
                    <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
                    <span className="hidden xs:inline">Col</span>
                    <ChevronDownIcon className="h-3 w-3 ml-0.5" />
                  </button>
                </div>
              )}

              {/* Filters Button - Stretched */}
              <div className="relative flex-1">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border ${
                    showFilters || Object.keys(filters).some(key => filters[key])
                      ? 'bg-blue-50 text-blue-700 border-blue-300'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  title="Filters"
                >
                  <FunnelIcon className="h-3 w-3 mr-0.5" />
                  <span className="hidden xs:inline">Filter</span>
                  {Object.keys(filters).some(key => filters[key]) && (
                    <span className="ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {Object.values(filters).filter(Boolean).length}
                    </span>
                  )}
                </button>
              </div>

              {/* Density Control - Stretched */}
              <div className="relative flex-1">
                <button
                  onClick={() => setShowDensityControls(!showDensityControls)}
                  className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                  title="Density"
                >
                  <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
                  <span className="hidden xs:inline">{displayDensity.charAt(0).toUpperCase() + displayDensity.slice(1)}</span>
                  <ChevronDownIcon className="h-3 w-3 ml-0.5" />
                </button>
              </div>

              {/* Create Button - Stretched */}
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-3 w-3 mr-0.5" />
                <span className="hidden xs:inline">Add</span>
              </button>
            </div>
          </div>

          {/* Desktop Layout - Horizontal */}
          <div className="hidden lg:flex items-center justify-between gap-4">
          {/* Search Bar and Filters */}
          <div className="flex items-center gap-3 flex-1 max-w-md">
            <div className="relative flex-1">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search payments by amount, method, status..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
            
            {/* Filters Dropdown */}
            <div className="relative dropdown-container">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  showFilters || Object.keys(filters).some(key => filters[key])
                    ? 'bg-blue-50 text-blue-700 border-blue-300'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
                title="Show/hide filters"
              >
                <FunnelIcon className="h-4 w-4 mr-2" />
                Filters
                {Object.keys(filters).some(key => filters[key]) && (
                  <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {Object.values(filters).filter(Boolean).length}
                  </span>
                )}
                <ChevronDownIcon className="h-4 w-4 ml-2" />
              </button>

              {/* Filters Dropdown */}
              {showFilters && (
                <div className="absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-gray-900">Filters</h3>
                      <button
                        onClick={() => setShowFilters(false)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          Status
                        </label>
                        <select
                          value={filters.status || ''}
                          onChange={(e) => {
                            const newFilters = { ...filters }
                            if (e.target.value) {
                              newFilters.status = e.target.value
                            } else {
                              delete newFilters.status
                            }
                            setFilters(newFilters)
                          }}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">All statuses</option>
                          <option value="pending">Pending</option>
                          <option value="completed">Completed</option>
                          <option value="failed">Failed</option>
                          <option value="refunded">Refunded</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          Payment Method
                        </label>
                        <select
                          value={filters.paymentMethod || ''}
                          onChange={(e) => {
                            const newFilters = { ...filters }
                            if (e.target.value) {
                              newFilters.paymentMethod = e.target.value
                            } else {
                              delete newFilters.paymentMethod
                            }
                            setFilters(newFilters)
                          }}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">All methods</option>
                          <option value="credit_card">Credit Card</option>
                          <option value="bank_transfer">Bank Transfer</option>
                          <option value="paypal">PayPal</option>
                          <option value="stripe">Stripe</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-end mt-4 pt-4 border-t border-gray-200">
                      <button
                        onClick={() => setFilters({})}
                        className="text-sm text-gray-600 hover:text-gray-800"
                      >
                        Clear filters
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">View:</span>
              <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                    viewMode === 'list'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="List view"
                >
                  <ListBulletIcon className="h-5 w-5" />
                  <span className="text-sm font-medium">List</span>
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                    viewMode === 'grid'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="Grid view"
                >
                  <Squares2X2Icon className="h-5 w-5" />
                  <span className="text-sm font-medium">Grid</span>
                </button>
              </div>
            </div>

            {/* Columns Control - Only show in grid view */}
            {viewMode === 'grid' && (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Columns:</span>
                <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                  <button
                    onClick={() => setGridColumns(1)}
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      gridColumns === 1
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title="1 column"
                  >
                    1
                  </button>
                  <button
                    onClick={() => setGridColumns(2)}
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      gridColumns === 2
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title="2 columns"
                  >
                    2
                  </button>
                  <button
                    onClick={() => setGridColumns(3)}
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      gridColumns === 3
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title="3 columns"
                  >
                    3
                  </button>
                  <button
                    onClick={() => setGridColumns(4)}
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      gridColumns === 4
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    title="4 columns"
                  >
                    4
                  </button>
                </div>
              </div>
            )}

            {/* Columns Dropdown - Only show in list view */}
            {viewMode === 'list' && (
              <div className="relative">
                <button
                  onClick={() => setShowColumnControls(!showColumnControls)}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  title="Column visibility"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                  Columns
                  <ChevronDownIcon className="h-4 w-4 ml-2" />
                </button>

                {/* Columns Dropdown */}
                {showColumnControls && (
                  <div className="absolute top-full right-0 mt-1 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="p-3">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                      <div className="space-y-2">
                        {availableColumns.map((column) => (
                          <label key={column.key} className={`flex items-center ${column.key === 'amount' ? 'opacity-50 cursor-not-allowed' : ''}`}>
                            <input
                              type="checkbox"
                              checked={visibleColumns.includes(column.key)}
                              disabled={column.key === 'amount'}
                              onChange={(e) => {
                                if (column.key === 'amount') return
                                if (e.target.checked) {
                                  setVisibleColumns(prev => [...prev, column.key])
                                } else {
                                  setVisibleColumns(prev => prev.filter(col => col !== column.key))
                                }
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              {column.label}
                              {column.key === 'amount' && <span className="text-xs text-gray-500 ml-1">(Fixed)</span>}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Density Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowDensityControls(!showDensityControls)}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                title="Select density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                {displayDensity.charAt(0).toUpperCase() + displayDensity.slice(1)}
                <ChevronDownIcon className="h-4 w-4 ml-2" />
              </button>

              {/* Density Dropdown */}
              {showDensityControls && (
                <div className="absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="p-1">
                    {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                      <button
                        key={option}
                        onClick={() => {
                          setDisplayDensity(option)
                          setShowDensityControls(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                          displayDensity === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                        }`}
                      >
                        {option.charAt(0).toUpperCase() + option.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Create Button */}
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Payment
            </button>


            {/* Density */}


          </div>
        </div>

        {/* Mobile Dropdowns */}
        {/* Filters Dropdown - Mobile */}
        {showFilters && (
          <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900">Filters</h3>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={filters.status || ''}
                  onChange={(e) => {
                    const newFilters = { ...filters }
                    if (e.target.value) {
                      newFilters.status = e.target.value
                    } else {
                      delete newFilters.status
                    }
                    setFilters(newFilters)
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All statuses</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <select
                  value={filters.paymentMethod || ''}
                  onChange={(e) => {
                    const newFilters = { ...filters }
                    if (e.target.value) {
                      newFilters.paymentMethod = e.target.value
                    } else {
                      delete newFilters.paymentMethod
                    }
                    setFilters(newFilters)
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All methods</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="paypal">PayPal</option>
                  <option value="stripe">Stripe</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={() => setFilters({})}
                className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
              >
                Clear All
              </button>
            </div>
          </div>
        )}

        {/* Column Selector Dropdown - Mobile */}
        {showColumnControls && (
          <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
            {availableColumns.map((column) => (
              <label key={column.key} className="flex items-center space-x-2 py-1">
                <input
                  type="checkbox"
                  checked={visibleColumns.includes(column.key)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setVisibleColumns(prev => [...prev, column.key])
                    } else {
                      setVisibleColumns(prev => prev.filter(col => col !== column.key))
                    }
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{column.label}</span>
              </label>
            ))}
          </div>
        )}

        {/* Density Dropdown - Mobile */}
        {showDensityControls && (
          <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-2">
            {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
              <button
                key={option}
                onClick={() => {
                  setDisplayDensity(option)
                  setShowDensityControls(false)
                }}
                className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                  displayDensity === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                }`}
              >
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Stats Overview */}
      <div className="px-4 pt-1 pb-1 bg-purple-100">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {statsData.map((stat, index) => (
            <div key={index} className="flex items-center space-x-3">
              <svg className={`w-8 h-8 ${stat.color}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={stat.icon} />
              </svg>
              <div className="flex items-center space-x-2">
                <div className={`text-3xl font-bold ${stat.color}`}>{stat.value}</div>
                <div className="text-base text-gray-600">{stat.label}</div>
              </div>
            </div>
          ))}
        </div>
      </div>


        {/* Bulk Actions Bar */}
        {showBulkActions && selectedPayments.length > 0 && (viewMode === 'list' || viewMode === 'grid') && (
          <div className="bg-purple-50 border border-purple-200 rounded-lg px-4 py-2 shadow-sm mt-2 mb-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 font-semibold text-xs">
                      {selectedPayments.length}
                </span>
              </div>
                  <span className="text-xs font-medium text-purple-900">
                    payment{selectedPayments.length === 1 ? '' : 's'} selected
                  </span>
                </div>
                
                <div className="flex items-center space-x-1.5">
                <button
                  onClick={() => handleBulkAction('refund', selectedPayments)}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 border border-purple-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500"
                    title="Refund selected payments"
                >
                    <ArrowPathIcon className="h-3 w-3 mr-1" />
                  Refund
                </button>
                  
                <button
                  onClick={() => handleBulkAction('download', selectedPayments)}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                    title="Download selected payments"
                >
                  <ArrowDownTrayIcon className="h-3 w-3 mr-1" />
                  Download
                </button>
                  
                <button
                  onClick={() => showBulkDeleteConfirmation(selectedPayments)}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500"
                    title="Delete selected payments"
                >
                  <TrashIcon className="h-3 w-3 mr-1" />
                  Delete
                  </button>
                </div>
              </div>
              
              <div className="flex items-center space-x-1.5">
                <button
                  onClick={handleClearSelection}
                  className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500"
                  title="Clear selection"
                >
                  <XMarkIcon className="h-3 w-3 mr-1" />
                  Clear
                </button>
              </div>
            </div>
          </div>
        )}

      {/* Main Content */}
      <div className={viewMode === 'list' ? 'p-4' : viewMode === 'grid' ? 'pt-3' : ''}>
        {payments.length === 0 ? (
          <div className="text-center py-12">
            <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {debouncedSearchQuery || Object.values(filters).some(v => v)
                ? 'Try adjusting your search or filters'
                : 'Get started by creating a new payment for this invoice'
              }
            </p>
            {!debouncedSearchQuery && !Object.values(filters).some(v => v) && (
              <div className="mt-6">
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 cursor-pointer"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Payment
                </button>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* List View */}
            {viewMode === 'list' && (
              <div>
                <div className="overflow-x-auto">
                  <div className="min-w-full inline-block align-middle">
                    <table key="payments-table" className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-200 border-b border-gray-300">
                    <tr>
                      <th className="px-6 py-2 text-left">
                        <input
                          type="checkbox"
                          checked={selectedPayments.length === paginatedPayments.length && paginatedPayments.length > 0}
                          onChange={handleSelectAll}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </th>
                      {availableColumns
                        .filter(col => visibleColumns.includes(col.key))
                        .map((column) => (
                          <th
                            key={column.key}
                            className="px-6 py-2 text-left text-xs font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onClick={() => column.sortable && handleSort(column.key)}
                          >
                            <div className="flex items-center space-x-1">
                              <span>{column.label}</span>
                              {column.sortable && (
                                sortBy === column.key ? (
                                sortOrder === 'asc' ? (
                                  <ArrowUpIcon className="h-3 w-3 text-green-600" />
                                ) : (
                                  <ArrowDownIcon className="h-3 w-3 text-green-600" />
                                  )
                                ) : (
                                  <ArrowUpIcon className="h-3 w-3 text-gray-400" />
                                )
                              )}
                            </div>
                          </th>
                        ))}
                      <th className="px-6 py-2 text-right text-xs font-medium text-gray-900 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {paginatedPayments.map((payment) => (
                      <PaymentRow
                        key={payment.id}
                        payment={payment}
                        isSelected={selectedPayments.includes(String(payment.id))}
                        onSelect={() => handleSelectPayment(String(payment.id))}
                        onAction={(action) => handleAction(action, payment)}
                        onPaymentSelect={onPaymentSelect}
                        selectedPayment={selectedPayment}
                        activeSection={activeSection}
                        visibleColumns={visibleColumns}
                        displayDensity={displayDensity}
                        viewMode={viewMode}
                        actionLoading={actionLoading}
                        formatDate={formatDate}
                        formatCurrency={formatCurrency}
                        getStatusColor={getStatusColor}
                        getStatusIcon={getStatusIcon}
                        getPaymentMethodIcon={getPaymentMethodIcon}
                      />
                    ))}
                  </tbody>
                </table>
                  </div>
                </div>
              </div>
            )}

            {/* Grid View */}
            {viewMode === 'grid' && (
              <div>
                <div className={showBulkActions && selectedPayments.length > 0 ? "mt-3" : ""}>
                  <div className={`grid gap-4 ${
                  gridColumns === 1 ? 'grid-cols-1' :
                  gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' :
                  gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                  'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                }`}>
                {paginatedPayments.map((payment) => (
                  <PaymentRow
                    key={payment.id}
                    payment={payment}
                    isSelected={selectedPayments.includes(String(payment.id))}
                    onSelect={() => handleSelectPayment(String(payment.id))}
                    onAction={(action) => handleAction(action, payment)}
                    onPaymentSelect={onPaymentSelect}
                    selectedPayment={selectedPayment}
                    activeSection={activeSection}
                    visibleColumns={visibleColumns}
                    displayDensity={displayDensity}
                    viewMode={viewMode}
                    actionLoading={actionLoading}
                    formatDate={formatDate}
                    formatCurrency={formatCurrency}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPaymentMethodIcon={getPaymentMethodIcon}
                  />
                ))}
                  </div>
                </div>
              </div>
            )}

            {/* Card View */}
            {viewMode === 'card' && (
              <div className="space-y-4">
                {paginatedPayments.map((payment) => (
                  <PaymentRow
                    key={payment.id}
                    payment={payment}
                    isSelected={selectedPayments.includes(String(payment.id))}
                    onSelect={() => handleSelectPayment(String(payment.id))}
                    onAction={(action) => handleAction(action, payment)}
                    onPaymentSelect={onPaymentSelect}
                    selectedPayment={selectedPayment}
                    activeSection={activeSection}
                    visibleColumns={visibleColumns}
                    displayDensity={displayDensity}
                    viewMode={viewMode}
                    actionLoading={actionLoading}
                    formatDate={formatDate}
                    formatCurrency={formatCurrency}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPaymentMethodIcon={getPaymentMethodIcon}
                  />
                ))}
              </div>
            )}

            {/* Responsive Pagination */}
            {(filteredPayments.length > 0 || payments.length > 0) && (
              <ResponsivePagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                itemsPerPage={pageSize}
                onItemsPerPageChange={handleItemsPerPageChange}
                totalItems={filteredPayments.length}
                startIndex={startIndex}
                endIndex={endIndex}
                itemsPerPageOptions={[5, 10, 20, 25, 50, 100]}
                showItemsPerPage={true}
                showPageInfo={true}
              />
            )}
          </>
        )}
      </div>

      {/* Modals */}
      <AnimatePresence>
        {isCreateModalOpen && (() => {
          const { EnhancedPaymentFormModalWrapper } = require('./payment-modal')
          return (
            <EnhancedPaymentFormModalWrapper
              isOpen={isCreateModalOpen}
              onClose={() => setIsCreateModalOpen(false)}
              onSubmit={handleCreate}
              invoice={invoice}
              client={client}
              project={project}
            />
          )
        })()}

        {isEditModalOpen && editingPayment && (() => {
          const { EnhancedPaymentFormModalWrapper } = require('./payment-modal')
          return (
            <EnhancedPaymentFormModalWrapper
              isOpen={isEditModalOpen}
              onClose={() => {
                setIsEditModalOpen(false)
                setEditingPayment(null)
              }}
              onSubmit={(data: any) => handleUpdate(String(editingPayment.id), data)}
              invoice={invoice}
              client={client}
              project={project}
              initialData={editingPayment}
            />
          )
        })()}
      </AnimatePresence>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        title="Delete Payment"
        message={(() => {
          if (!confirmationModal.verificationData) return `Are you sure you want to delete payment $${paymentToDelete?.amount}?`
          
          if (confirmationModal.verificationData.canDelete) {
            return `Delete payment $${paymentToDelete?.amount}?`
          } else {
            return `Cannot delete payment $${paymentToDelete?.amount}`
          }
        })()}
        details={(() => {
          if (!confirmationModal.verificationData) return "This action cannot be undone. All associated data will be permanently removed."
          
          if (confirmationModal.verificationData.canDelete) {
            return "This payment has no dependencies. It will be permanently removed. This action cannot be undone."
          } else {
            return "Please remove all dependencies before attempting to delete this payment."
          }
        })()}
        confirmText={confirmationModal.verificationData?.canDelete ? "Delete Payment" : "Close"}
        cancelText="Cancel"
        onConfirm={confirmationModal.onConfirm}
        onCancel={confirmationModal.onCancel}
        type={confirmationModal.verificationData?.canDelete ? 'danger' : 'verification'}
        showVerification={true}
        verificationData={confirmationModal.verificationData ? {
          canDelete: confirmationModal.verificationData.canDelete,
          reason: confirmationModal.verificationData.reason,
          dependencies: confirmationModal.verificationData.dependencies
        } : undefined}
      />
      
      {/* Notification System - Removed to avoid duplication with parent component */}
    </div>
  )
}
