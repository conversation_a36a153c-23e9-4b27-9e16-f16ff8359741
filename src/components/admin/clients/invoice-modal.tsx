'use client'

import '@/styles/components/modals.css'
import '@/styles/components/forms.css'
import '@/styles/components/buttons.css'
import {
  DocumentTextIcon,
  PlusIcon,
  TrashIcon,
  XMarkIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  TagIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import React, { useEffect, useState } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON>dalFooter, 
  But<PERSON> 
} from '../shared/modal-system'

interface InvoiceItem {
  id?: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  initialData?: any
  client: {
    id: string | number
    companyName: string
    contactName: string
    contactEmail: string
  }
  project: {
    id: string | number
    name: string
    description: string
  }
}

export function InvoiceModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  client,
  project
}: InvoiceModalProps) {
  const [loading, setLoading] = useState(false)
  const [itemsLoading, setItemsLoading] = useState(false)
  const [formData, setFormData] = useState({
    invoiceNumber: '',
    dueDate: '',
    status: 'DRAFT',
    description: '',
    taxRate: 0,
    subtotal: 0,
    taxAmount: 0,
    totalAmount: 0,
    paidAt: ''
  })

  const [items, setItems] = useState<InvoiceItem[]>([
    { id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }
  ])

  // Calculate totals when items or tax rate changes
  useEffect(() => {
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0)
    const taxAmount = (subtotal * formData.taxRate) / 100
    const totalAmount = subtotal + taxAmount

    setFormData(prev => ({
      ...prev,
      subtotal,
      taxAmount,
      totalAmount
    }))
  }, [items, formData.taxRate])

  // Load initial data
  useEffect(() => {
    const loadInvoiceData = async () => {
      if (initialData) {
        setFormData({
          invoiceNumber: initialData.invoiceNumber || '',
          dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',
          status: initialData.status || 'DRAFT',
          description: initialData.description || '',
          taxRate: Number(initialData.taxRate) || 0,
          subtotal: Number(initialData.subtotal) || 0,
          taxAmount: Number(initialData.taxAmount) || 0,
          totalAmount: Number(initialData.totalAmount) || 0,
          paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''
        })

        // Load existing items from API
        try {
          setItemsLoading(true)
          const invoiceId = String(initialData.id)
          console.log('Fetching items for invoice ID:', invoiceId)
          const response = await fetch(`/api/admin/invoices/${invoiceId}/items`)

          console.log('Items fetch response status:', response.status, response.statusText)

          if (response.ok) {
            const result = await response.json()
            console.log('Items fetch result:', result)

            if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {
              // Parse Decimal.js objects with format {s, e, d}
              const parseDecimal = (decimalObj: any): number => {
                if (!decimalObj || typeof decimalObj !== 'object') return 0

                const { s, e, d } = decimalObj
                if (s === undefined || e === undefined || !Array.isArray(d)) return 0

                // Convert digits array to number
                const digits = d.join('')
                if (!digits) return 0

                // Calculate the actual value: sign * digits * 10^(exponent - digits.length + 1)
                const value = s * parseFloat(digits) * Math.pow(10, e - digits.length + 1)
                return isNaN(value) ? 0 : value
              }

              // Map database items to form items
              const mappedItems = result.data.map((item: any) => ({
                id: String(item.id),
                description: String(item.description || ''),
                quantity: parseDecimal(item.quantity),
                unitPrice: parseDecimal(item.unitprice),
                totalPrice: parseDecimal(item.totalprice)
              }))

              console.log('Mapped items:', mappedItems)
              setItems(mappedItems)
            } else {
              // No items found, use default empty item
              console.log('No items found in response, using default empty item')
              setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
            }
          } else {
            // API error, use default empty item
            console.error('Failed to fetch invoice items:', response.status, response.statusText)
            setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
          }
        } catch (error) {
          // Network or other error, use default empty item
          console.error('Error loading invoice items:', error)
          setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
        } finally {
          setItemsLoading(false)
        }
      } else {
        // Reset form for new invoice
        setFormData({
          invoiceNumber: '',
          dueDate: '',
          status: 'DRAFT',
          description: '',
          taxRate: 0,
          subtotal: 0,
          taxAmount: 0,
          totalAmount: 0,
          paidAt: ''
        })
        setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
      }
    }

    if (isOpen) {
      loadInvoiceData()
    }
  }, [initialData, isOpen])

  const addItem = () => {
    setItems([...items, {
      id: `temp-${Date.now()}`, // Temporary ID for new items
      description: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0
    }])
  }

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index))
    }
  }

  const updateItem = (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...items]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    // Auto-calculate total price when quantity or unit price changes
    if (field === 'quantity' || field === 'unitPrice') {
      const quantity = field === 'quantity' ? Number(value) || 0 : updatedItems[index].quantity
      const unitPrice = field === 'unitPrice' ? Number(value) || 0 : updatedItems[index].unitPrice
      updatedItems[index].totalPrice = quantity * unitPrice
    }

    setItems(updatedItems)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate items
      const validItems = items.filter(item => item.description.trim() !== '')

      if (validItems.length === 0) {
        alert('Please add at least one item to the invoice.')
        setLoading(false)
        return
      }

      // Validate that all items have positive whole number quantities and prices
      const invalidItems = validItems.filter(item => {
        // Handle floating-point precision issues by checking if quantity is close to an integer
        const roundedQuantity = Math.round(item.quantity)
        const isCloseToInteger = Math.abs(item.quantity - roundedQuantity) < 0.0001
        const isQuantityValid = roundedQuantity > 0 && (Number.isInteger(item.quantity) || isCloseToInteger)
        const isPriceValid = item.unitPrice >= 0
        
        if (!isQuantityValid || !isPriceValid) {
          console.log('Invalid item:', {
            description: item.description,
            originalQuantity: item.quantity,
            roundedQuantity: roundedQuantity,
            quantityType: typeof item.quantity,
            isInteger: Number.isInteger(item.quantity),
            isCloseToInteger: isCloseToInteger,
            unitPrice: item.unitPrice,
            isQuantityValid,
            isPriceValid
          })
        }
        
        return !isQuantityValid || !isPriceValid
      })

      if (invalidItems.length > 0) {
        console.log('Invalid items found:', invalidItems)
        alert('All items must have positive whole number quantities and non-negative unit prices.')
        setLoading(false)
        return
      }

      const submitData = {
        ...formData,
        items: validItems.map(item => ({
          ...item,
          // Round quantity to handle floating-point precision issues when submitting
          quantity: Math.round(item.quantity),
          // Remove temporary IDs for new items
          id: item.id?.startsWith('temp-') ? undefined : item.id
        })),
        clientId: client.id,
        projectId: project.id
      }

      console.log('Submitting invoice data:', submitData)
      console.log('Valid items:', validItems)
      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Error submitting invoice:', error)
      alert('Failed to save invoice. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDownloadPDF = async () => {
    try {
      console.log('Starting PDF download with data:', { formData, client, project })
      
      // Try basic HTML-to-PDF approach first
      let response = await fetch('/api/invoices/generate-pdf-basic', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceData: {
            invoiceNumber: formData.invoiceNumber || (initialData ? `INV-${String(initialData.id).padStart(4, '0')}` : ''),
            dueDate: formData.dueDate,
            status: formData.status,
            description: formData.description,
            taxRate: formData.taxRate,
            subtotal: formData.subtotal,
            taxAmount: formData.taxAmount,
            totalAmount: formData.totalAmount,
            paidAt: formData.paidAt,
            items: items.filter(item => item.description.trim() !== '')
          },
          client,
          project
        })
      })

      console.log('Basic PDF API response status:', response.status)

      if (response.ok) {
        // Open the HTML in a new window for printing/saving as PDF
        const htmlContent = await response.text()
        const printWindow = window.open('', '_blank')
        if (printWindow) {
          printWindow.document.write(htmlContent)
          printWindow.document.close()
          printWindow.focus()
          // The HTML includes auto-print script, so it will automatically open print dialog
          console.log('PDF download completed successfully via HTML')
          return
        }
      }

      // If basic approach fails, try simple PDF generation
      console.log('Basic PDF failed, trying simple PDF approach...')
      response = await fetch('/api/invoices/generate-pdf-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceData: {
            invoiceNumber: formData.invoiceNumber || (initialData ? `INV-${String(initialData.id).padStart(4, '0')}` : ''),
            dueDate: formData.dueDate,
            status: formData.status,
            description: formData.description,
            taxRate: formData.taxRate,
            subtotal: formData.subtotal,
            taxAmount: formData.taxAmount,
            totalAmount: formData.totalAmount,
            paidAt: formData.paidAt,
            items: items.filter(item => item.description.trim() !== '')
          },
          client,
          project
        })
      })

      console.log('Simple PDF API response status:', response.status)

      // If simple PDF fails, try the original Puppeteer approach
      if (!response.ok) {
        console.log('Simple PDF failed, trying Puppeteer approach...')
        response = await fetch('/api/invoices/generate-pdf', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            invoiceData: {
              invoiceNumber: formData.invoiceNumber || (initialData ? `INV-${String(initialData.id).padStart(4, '0')}` : ''),
              dueDate: formData.dueDate,
              status: formData.status,
              description: formData.description,
              taxRate: formData.taxRate,
              subtotal: formData.subtotal,
              taxAmount: formData.taxAmount,
              totalAmount: formData.totalAmount,
              paidAt: formData.paidAt,
              items: items.filter(item => item.description.trim() !== '')
            },
            client,
            project
          })
        })
        console.log('Puppeteer PDF API response status:', response.status)
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('PDF generation failed:', errorData)
        throw new Error(`Failed to generate PDF: ${errorData.details || errorData.error || 'Unknown error'}`)
      }

      const blob = await response.blob()
      console.log('PDF blob created, size:', blob.size)
      
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `invoice-${formData.invoiceNumber || (initialData ? `INV-${String(initialData.id).padStart(4, '0')}` : '')}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      console.log('PDF download completed successfully')
    } catch (error) {
      console.error('Download error:', error)
      alert(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }


  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      subtitle={`${client.companyName} - ${project.name}`}
      icon={<DocumentTextIcon className="h-6 w-6 text-white" />}
      iconColor="blue"
      disableOverlayClick={true}
    >
      <form onSubmit={handleSubmit} className="modal-form" onClick={(e) => e.stopPropagation()}>
          {/* Two Column Layout */}
          <div className="grid grid-cols-3 gap-4">
            {/* Left Column - Invoice Details and Items */}
            <div className="col-span-2">
              {/* Invoice Details Section */}
              <div className="modal-form-section sample-style">
                <div className="modal-form-section-header sample-style">
                  <h3 className="modal-form-section-title sample-style">
                    <DocumentTextIcon className="modal-form-section-icon sample-style" />
                    Invoice Details
                  </h3>
                </div>
                
                {/* Top Row - All fields except description */}
                <div className="grid grid-cols-4 gap-3 mb-3">
                  <div className="modal-form-field">
                    <label className="modal-form-label">Invoice Number</label>
                    <input
                      type="text"
                      value={formData.invoiceNumber || (initialData ? `INV-${String(initialData.id).padStart(4, '0')}` : '')}
                      onChange={(e) => setFormData({ ...formData, invoiceNumber: e.target.value })}
                      placeholder="Auto-generated"
                      className="modal-form-input"
                    />
                  </div>
                  <div className="modal-form-field">
                    <label className="modal-form-label">Due Date <span className="modal-required">*</span></label>
                    <input
                      type="date"
                      required
                      value={formData.dueDate}
                      onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                      className="modal-form-input"
                    />
                  </div>
                  <div className="modal-form-field">
                    <label className="modal-form-label">Status</label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                      className="modal-form-select"
                    >
                      <option value="DRAFT">Draft</option>
                      <option value="SENT">Sent</option>
                      <option value="PAID">Paid</option>
                      <option value="OVERDUE">Overdue</option>
                    </select>
                  </div>
                  <div className="modal-form-field">
                    <label className="modal-form-label">Tax Rate (%)</label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={formData.taxRate}
                      onChange={(e) => setFormData({ ...formData, taxRate: parseFloat(e.target.value) || 0 })}
                      className="modal-form-input"
                      placeholder="0.00"
                    />
                  </div>
                </div>

                {/* Bottom Row - Description (full width) */}
                <div className="modal-form-field">
                  <label className="modal-form-label">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={2}
                    className="modal-form-textarea"
                    placeholder="Invoice description..."
                  />
                </div>
              </div>

              {/* Invoice Items Section - Compact */}
              <div className="modal-form-section sample-style">
            <div className="modal-form-section-header sample-style">
              <h3 className="modal-form-section-title sample-style">
                <CurrencyDollarIcon className="modal-form-section-icon sample-style green" />
                Invoice Items ({items.filter(item => item.description.trim() !== '').length})
              </h3>
              <Button
                type="button"
                onClick={addItem}
                variant="outline"
                size="sm"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add Item
              </Button>
            </div>

            {/* Single Container with Separate Fields */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              {/* Table Header */}
              <div className="grid grid-cols-12 text-sm font-semibold text-gray-700 bg-gray-100 py-2">
                <div className="col-span-5 pl-2 border-r border-gray-300">Description</div>
                <div className="col-span-2 text-center border-r border-gray-300">Qty</div>
                <div className="col-span-2 text-center border-r border-gray-300">Price</div>
                <div className="col-span-2 text-center border-r border-gray-300">Total</div>
                <div className="col-span-1 text-center">×</div>
              </div>

              {/* Items Rows */}
              {itemsLoading ? (
                <div className="p-4 text-center">
                  <div className="inline-flex items-center space-x-2 text-gray-500">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    <span className="text-sm">Loading...</span>
                  </div>
                </div>
              ) : (
                items.map((item, index) => (
                  <div key={item.id || `item-${index}`} className={`grid grid-cols-12 items-center py-0.5 hover:bg-blue-50 transition-colors ${index < items.length - 1 ? 'border-b border-gray-100' : ''}`}>
                    {/* Description Field */}
                    <div className="col-span-5 border-r border-gray-200">
                      <input
                        type="text"
                        required
                        value={item.description}
                        onChange={(e) => updateItem(index, 'description', e.target.value)}
                        className={`w-full text-sm border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-2 py-2 ${
                          item.description.trim() === '' ? 'text-red-500 placeholder-red-300' : 'text-gray-800'
                        }`}
                        placeholder="Item description"
                      />
                    </div>
                    
                    {/* Quantity Field */}
                    <div className="col-span-2 border-r border-gray-200">
                      <input
                        type="number"
                        required
                        min="1"
                        step="1"
                        value={item.quantity || ''}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0
                          updateItem(index, 'quantity', value)
                        }}
                        className={`w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 ${
                          item.quantity <= 0 ? 'text-red-500' : 'text-gray-800'
                        }`}
                        placeholder="1"
                      />
                    </div>
                    
                    {/* Unit Price Field */}
                    <div className="col-span-2 border-r border-gray-200">
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={item.unitPrice || ''}
                        onChange={(e) => {
                          const value = parseFloat(e.target.value) || 0
                          updateItem(index, 'unitPrice', value)
                        }}
                        className={`w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 ${
                          item.unitPrice < 0 ? 'text-red-500' : 'text-gray-800'
                        }`}
                        placeholder="0.00"
                      />
                    </div>
                    
                    {/* Total Display */}
                    <div className="col-span-2 border-r border-gray-200 text-sm font-semibold text-gray-700 text-center py-2">
                      ${(item.totalPrice || 0).toFixed(2)}
                    </div>
                    
                    {/* Remove Button */}
                    <div className="col-span-1 flex justify-center">
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        disabled={items.length === 1}
                        className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>

                {/* Validation Messages */}
                {items.filter(item => item.description.trim() !== '').length === 0 && (
                  <div className="modal-message warning">
                    <div className="modal-message-content">
                      <div className="modal-message-icon warning">⚠️</div>
                      <div className="modal-message-text warning">
                        Please add at least one item to the invoice.
                      </div>
                    </div>
                  </div>
                )}

                {items.some(item => item.description.trim() !== '' && (item.quantity <= 0 || item.unitPrice < 0)) && (
                  <div className="modal-message error">
                    <div className="modal-message-content">
                      <div className="modal-message-icon error">❌</div>
                      <div className="modal-message-text error">
                        All items must have positive whole number quantities and non-negative unit prices.
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column - Invoice Summary */}
            <div className="col-span-1">
              <div className="modal-form-section sample-style">
                <div className="modal-form-section-header sample-style">
                  <h3 className="modal-form-section-title sample-style">
                    <CurrencyDollarIcon className="modal-form-section-icon sample-style orange" />
                    Invoice Summary
                  </h3>
                </div>
                
                {/* Summary Cards */}
                <div className="space-y-2">
                  {/* Subtotal Card */}
                  <div className="bg-white border border-gray-200 rounded-lg p-3">
                    <div className="text-xs font-medium text-gray-500 mb-1">Subtotal</div>
                    <div className="text-lg font-bold text-gray-800">${formData.subtotal.toFixed(2)}</div>
                  </div>

                  {/* Tax Card */}
                  <div className="bg-white border border-gray-200 rounded-lg p-3">
                    <div className="text-xs font-medium text-gray-500 mb-1">Tax ({formData.taxRate}%)</div>
                    <div className="text-lg font-bold text-gray-800">${formData.taxAmount.toFixed(2)}</div>
                  </div>

                  {/* Total Card */}
                  <div className="bg-white border border-gray-200 rounded-lg p-3">
                    <div className="text-xs font-medium text-gray-500 mb-1">Total Amount</div>
                    <div className="text-lg font-bold text-blue-600">${formData.totalAmount.toFixed(2)}</div>
                  </div>

                  {/* Payment Tracking */}
                  <div className="bg-white border border-gray-200 rounded-lg p-3">
                    <div className="text-xs font-medium text-gray-500 mb-2">Payment Status</div>
                    
                    {/* Amount Paid */}
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Amount Paid:</span>
                      <span className="text-sm font-semibold text-green-600">${(initialData?.amountPaid || 0).toFixed(2)}</span>
                    </div>
                    
                    {/* Balance Due */}
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Balance Due:</span>
                      <span className="text-sm font-semibold text-red-600">${(formData.totalAmount - (initialData?.amountPaid || 0)).toFixed(2)}</span>
                    </div>
                    
                    {/* Payment Status Badge */}
                    <div className="mt-2">
                      {formData.totalAmount - (initialData?.amountPaid || 0) <= 0 ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                          Paid in Full
                        </span>
                      ) : (initialData?.amountPaid || 0) > 0 ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></div>
                          Partially Paid
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                          Unpaid
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Payment History */}
                  {(initialData?.payments && initialData.payments.length > 0) && (
                    <div className="bg-white border border-gray-200 rounded-lg p-3">
                      <div className="text-xs font-medium text-gray-500 mb-2">Recent Payments</div>
                      <div className="space-y-1">
                        {initialData.payments.slice(0, 3).map((payment: any, index: number) => (
                          <div key={index} className="flex justify-between items-center text-xs">
                            <span className="text-gray-600">{new Date(payment.paymentDate).toLocaleDateString()}</span>
                            <span className="font-medium text-green-600">${payment.amount.toFixed(2)}</span>
                          </div>
                        ))}
                        {initialData.payments.length > 3 && (
                          <div className="text-xs text-gray-500 text-center pt-1">
                            +{initialData.payments.length - 3} more payments
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </form>
        {/* Footer with Action Buttons */}
        <div
          style={{
            backgroundColor: 'white',
            padding: '24px 24px 0 24px',
            borderTop: '1px solid #e2e8f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            minHeight: '60px',
            opacity: 1,
            transform: 'none',
            borderBottomLeftRadius: '12px',
            borderBottomRightRadius: '12px',
          }}
        >
          {/* Status Message - Left Aligned */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#64748b',
            fontSize: '14px',
            position: 'absolute',
            left: '24px',
            top: '50%',
            transform: 'translateY(-50%)',
          }}>
            <DocumentTextIcon style={{ width: '16px', height: '16px' }} />
            <span>{initialData ? 'Last updated: ' + new Date(initialData.updatedAt || Date.now()).toLocaleDateString() : 'Creating new invoice'}</span>
          </div>
          
          {/* Action Buttons - Auto-Centered */}
          <div style={{
            display: 'flex',
            gap: '12px',
            position: 'absolute',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 11,
          }}>
            <button
              type="button"
              onClick={handleDownloadPDF}
              style={{
                padding: '12px 24px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',
                transform: 'translateY(-2px)',
                marginRight: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#4b5563'
                e.currentTarget.style.transform = 'translateY(-3px)'
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#6b7280'
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)'
              }}
            >
              <ArrowDownTrayIcon style={{ width: '12px', height: '12px' }} />
              View / Print
            </button>
            <button
              type="button"
              onClick={onClose}
              style={{
                padding: '12px 24px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',
                transform: 'translateY(-2px)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#4b5563'
                e.currentTarget.style.transform = 'translateY(-3px)'
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#6b7280'
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={loading}
              style={{
                padding: '12px 24px',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',
                transform: 'translateY(-2px)',
                opacity: loading ? 0.6 : 1,
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                  e.currentTarget.style.transform = 'translateY(-3px)'
                  e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)'
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#3b82f6'
                  e.currentTarget.style.transform = 'translateY(-2px)'
                  e.currentTarget.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)'
                }
              }}
            >
              {loading ? (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    border: '2px solid transparent',
                    borderTopColor: 'currentColor',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                  }} />
                  <span>Processing...</span>
                </div>
              ) : (
                initialData ? 'Update Invoice' : 'Create Invoice'
              )}
            </button>
          </div>
        </div>
    </Modal>
  )
}









