'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { XMarkIcon } from '@heroicons/react/24/outline'
import '@/styles/components/modals.css'
import '@/styles/components/forms.css'
import '@/styles/components/buttons.css'

// Enhanced drag and resize hook (matching sample modal)
export const useDraggableResizable = (isFormOpen: boolean) => {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [size, setSize] = useState({ width: 1200, height: 600 })
  const [isReady, setIsReady] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 })
  const elementRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const actualContentRef = useRef<HTMLDivElement>(null)
  const headerRef = useRef<HTMLDivElement>(null)
  const footerRef = useRef<HTMLDivElement>(null)
  const animationFrameRef = useRef<number | null>(null)

  // Enhanced mouse move handler (matching sample modal)
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && elementRef.current) {
      const newX = e.clientX - dragStart.x
      const newY = e.clientY - dragStart.y
      
      // Keep modal within viewport bounds
      const maxX = window.innerWidth - size.width
      const maxY = window.innerHeight - size.height
      
      const constrainedX = Math.max(0, Math.min(maxX, newX))
      const constrainedY = Math.max(0, Math.min(maxY, newY))
      
      // Immediate DOM update - no state updates during drag for maximum speed
      elementRef.current.style.left = `${constrainedX}px`
      elementRef.current.style.top = `${constrainedY}px`
      elementRef.current.style.transform = 'none'
      elementRef.current.style.transition = 'none'
    } else if (isResizing && elementRef.current) {
      const deltaX = e.clientX - resizeStart.x
      const deltaY = e.clientY - resizeStart.y
      
      // Get resize direction from the element that initiated the resize
      const resizeElement = document.querySelector('[data-resize-direction]')
      const direction = resizeElement?.getAttribute('data-resize-direction') || 'se'
      
      let newWidth = resizeStart.width
      let newHeight = resizeStart.height
      
      // Handle different resize directions
      if (direction.includes('e')) { // East (right)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width + deltaX))
      }
      if (direction.includes('w')) { // West (left)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width - deltaX))
      }
      if (direction.includes('s')) { // South (bottom)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height + deltaY))
      }
      if (direction.includes('n')) { // North (top)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height - deltaY))
      }
      
      // Immediate DOM update - no state updates during resize for maximum speed
      elementRef.current.style.width = `${newWidth}px`
      elementRef.current.style.height = `${newHeight}px`
      elementRef.current.style.transition = 'none'
    }
  }, [isDragging, isResizing, dragStart, resizeStart, size.width, size.height])

  const handleMouseUp = useCallback(() => {
    // Sync state with final DOM position for consistency
    if (elementRef.current && (isDragging || isResizing)) {
      const rect = elementRef.current.getBoundingClientRect()
      setPosition({ x: rect.left, y: rect.top })
      setSize({ width: rect.width, height: rect.height })
    }
    
    // Restore transitions when interaction ends
    if (elementRef.current) {
      elementRef.current.style.transition = 'box-shadow 0.2s ease'
    }
    
    // Clean up resize direction attribute
    const resizeElement = document.querySelector('[data-resize-direction]')
    if (resizeElement) {
      resizeElement.removeAttribute('data-resize-direction')
    }
    
    setIsDragging(false)
    setIsResizing(false)
  }, [isDragging, isResizing])

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    })
  }, [position])

  const handleResizeMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsResizing(true)
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    })
  }, [size])

  // Handle border resizing (matching sample modal)
  const handleBorderResizeMouseDown = useCallback((e: React.MouseEvent, direction: string) => {
    e.preventDefault()
    e.stopPropagation()
    setIsResizing(true)
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    })
    // Store resize direction
    e.currentTarget.setAttribute('data-resize-direction', direction)
  }, [size])

  // Modal positioning and auto-height calculation (matching sample modal)
  useEffect(() => {
    if (isFormOpen && typeof window !== 'undefined') {
      setIsReady(false) // Hide modal while calculating
      
      // Modal dimensions
      const modalWidth = 1200
      const initialHeight = 600
      
      // Calculate center position
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      
      // Center calculation with viewport bounds
      const left = Math.max(0, (viewportWidth - modalWidth) / 2)
      const top = Math.max(0, (viewportHeight - initialHeight) / 2)
      
      // Ensure modal stays within viewport
      const finalLeft = Math.min(left, viewportWidth - modalWidth)
      const finalTop = Math.min(top, viewportHeight - initialHeight)
      
      // Set initial size and position
      setSize({ width: modalWidth, height: initialHeight })
      setPosition({ x: finalLeft, y: finalTop })
      
      // Show modal and calculate auto-height
      setTimeout(() => {
        setIsReady(true)
        
        // Auto-height calculation after content renders
        setTimeout(() => {
          if (actualContentRef.current && headerRef.current && footerRef.current) {
            // Get the actual content height
            const contentHeight = actualContentRef.current.scrollHeight
            
            // Get the actual header height
            const headerHeight = headerRef.current.getBoundingClientRect().height
            
            // Get the actual footer height
            const footerHeight = footerRef.current.getBoundingClientRect().height
            
            // Calculate total height using actual measurements
            const totalHeight = contentHeight + headerHeight + footerHeight
            const maxHeight = viewportHeight * 0.95
            const newHeight = Math.min(maxHeight, totalHeight)
            
            // Re-center with new height
            const newTop = Math.max(0, (viewportHeight - newHeight) / 2)
            const finalNewTop = Math.min(newTop, viewportHeight - newHeight)
            
            setSize(prev => ({ ...prev, height: newHeight }))
            setPosition(prev => ({ ...prev, y: finalNewTop }))
          }
        }, 50)
      }, 10)
    } else {
      setIsReady(false)
    }
  }, [isFormOpen])

  // Event listeners for dragging and resizing (matching sample modal)
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove, { passive: true })
      document.addEventListener('mouseup', handleMouseUp, { passive: true })
      document.addEventListener('mouseleave', handleMouseUp, { passive: true })
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('mouseleave', handleMouseUp)
      }
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  // Handle window resize
  useEffect(() => {
    const handleWindowResize = () => {
      if (elementRef.current) {
        const rect = elementRef.current.getBoundingClientRect()
        const maxX = window.innerWidth - size.width
        const maxY = window.innerHeight - size.height
        
        if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
          const newX = Math.max(0, Math.min(maxX, position.x))
          const newY = Math.max(0, Math.min(maxY, position.y))
          setPosition({ x: newX, y: newY })
        }
      }
    }

    window.addEventListener('resize', handleWindowResize)
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [position, size])

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  return {
    position,
    size,
    isReady,
    isDragging,
    isResizing,
    handleMouseDown,
    handleResizeMouseDown,
    handleBorderResizeMouseDown,
    elementRef,
    contentRef,
    actualContentRef,
    headerRef,
    footerRef
  }
}

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  subtitle?: string
  children: React.ReactNode
  showCloseButton?: boolean
  icon?: React.ReactNode
  iconColor?: 'blue' | 'green' | 'purple' | 'orange' | 'red'
  disableOverlayClick?: boolean
}

export function Modal({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  showCloseButton = true,
  icon,
  iconColor = 'blue',
  disableOverlayClick = false
}: ModalProps) {
  const { 
    position, 
    size, 
    isReady,
    isDragging, 
    isResizing, 
    handleMouseDown, 
    handleResizeMouseDown, 
    handleBorderResizeMouseDown,
    elementRef,
    contentRef,
    actualContentRef,
    headerRef,
    footerRef
  } = useDraggableResizable(isOpen)

  // No click outside handler needed - handled directly on overlay

  if (!isOpen || !isReady) return null

  return (
    <>
      <style jsx>{`
        @keyframes modalAppear {
          0% {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
      
      {/* Modal Backdrop/Overlay */}
      <div
        className="modal-overlay"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.1)',
          zIndex: 99998,
          backdropFilter: 'blur(2px)',
        }}
        onClick={disableOverlayClick ? undefined : onClose}
      />
      
      {/* Modal Window */}
      <div
        ref={elementRef}
        className={`modal-container sample-style ${isDragging ? 'dragging' : 'default'} ${isResizing ? 'resizing' : ''}`}
        style={{
          position: 'fixed',
          zIndex: 99999,
          width: `${size.width}px`,
          height: `${size.height}px`,
          left: `${position.x}px`,
          top: `${position.y}px`,
          transition: isDragging || isResizing ? 'none' : 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Blue Header */}
        <div
          ref={headerRef}
          className={`modal-header-motion blue-gradient ${isDragging ? 'dragging' : 'default'}`}
          onMouseDown={handleMouseDown}
        >
          {/* Icon Container */}
          <div className="modal-header-motion-icon">
            {icon}
          </div>

          {/* Text Container */}
          <div className="modal-header-motion-text">
            <h3 className="modal-header-motion-title">
              {title}
            </h3>
            {subtitle && (
              <p className="modal-header-motion-subtitle">
                {subtitle}
              </p>
            )}
          </div>

          {/* Close Button */}
          {showCloseButton && (
            <button
              onClick={onClose}
              className="modal-header-motion-close"
            >
              <XMarkIcon className="modal-close-icon" />
            </button>
          )}
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className="modal-content sample-style"
        >
          <div
            ref={actualContentRef}
            className="modal-content-body sample-style"
          >
            <div className="modal-content-wrapper sample-style">
              {React.Children.map(children, (child) => {
                if (React.isValidElement(child) && child.type === ModalFooter) {
                  return React.cloneElement(child, { footerRef })
                }
                return child
              })}
            </div>
          </div>
        </div>

        {/* Border Resize Handles */}
        {/* Top border */}
        <div
          className="modal-resize-handle modal-resize-handle-top"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 'n')}
        />
        
        {/* Right border */}
        <div
          className="modal-resize-handle modal-resize-handle-right"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 'e')}
        />
        
        {/* Bottom border */}
        <div
          className="modal-resize-handle modal-resize-handle-bottom"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 's')}
        />
        
        {/* Left border */}
        <div
          className="modal-resize-handle modal-resize-handle-left"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 'w')}
        />
      </div>
    </>
  )
}

// Modal Content Component (matching sample modal)
interface ModalContentProps {
  children: React.ReactNode
}

export function ModalContent({ children }: ModalContentProps) {
  return (
    <div className="modal-content sample-style">
      <div className="modal-content-body sample-style">
        {children}
      </div>
    </div>
  )
}

// Modal Footer Component (matching sample modal)
interface ModalFooterProps {
  children: React.ReactNode
  className?: string
  footerRef?: React.RefObject<HTMLDivElement>
}

export function ModalFooter({ children, className = '', footerRef }: ModalFooterProps) {
  return (
    <div ref={footerRef} className={`modal-footer sample-style ${className}`}>
      <div className="modal-footer-buttons sample-style">
        {children}
      </div>
    </div>
  )
}

// Form Section Component
interface FormSectionProps {
  title: string
  icon?: React.ReactNode
  iconColor?: 'blue' | 'green' | 'purple' | 'orange' | 'red'
  children: React.ReactNode
  className?: string
  compact?: boolean
}

export function FormSection({ title, icon, iconColor = 'blue', children, className = '', compact = false }: FormSectionProps) {
  return (
    <div className={`modal-form-section ${compact ? 'compact' : 'default'} ${className}`}>
      <div className={`modal-form-section-header ${compact ? 'compact' : 'default'}`}>
        {icon && (
          <div className={`modal-form-section-motion-icon ${iconColor}`}>
            {icon}
          </div>
        )}
        <h3 className="modal-form-section-title">{title}</h3>
      </div>
      <div className={`modal-form-section-content ${compact ? 'compact' : 'default'}`}>
        {children}
      </div>
    </div>
  )
}

// Form Field Component
interface FormFieldProps {
  label: string
  required?: boolean
  error?: string
  children: React.ReactNode
  className?: string
}

export function FormField({ label, required = false, error, children, className = '' }: FormFieldProps) {
  return (
    <div className={`modal-form-field ${className}`}>
      <label className="modal-form-label">
        {label} {required && <span className="modal-required">*</span>}
      </label>
      {children}
      {error && (
        <p className="modal-form-error">{error}</p>
      )}
    </div>
  )
}

// Input Component
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
}

export function Input({ error, className = '', ...props }: InputProps) {
  return (
    <input
      className={`modal-form-input ${error ? 'is-invalid' : ''} ${className}`}
      {...props}
    />
  )
}

// Textarea Component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
}

export function Textarea({ error, className = '', ...props }: TextareaProps) {
  return (
    <textarea
      className={`modal-form-textarea ${error ? 'is-invalid' : ''} ${className}`}
      {...props}
    />
  )
}

// Button Component (matching sample modal)
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'warning' | 'info'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
}

export function Button({ 
  variant = 'primary', 
  size = 'md', 
  loading = false, 
  disabled, 
  children, 
  className = '', 
  ...props 
}: ButtonProps) {
  const getVariantClass = () => {
    switch (variant) {
      case 'primary': return 'btn sample-primary'
      case 'secondary': return 'btn sample-secondary'
      case 'outline': return 'btn btn-outline'
      case 'danger': return 'btn btn-danger'
      case 'success': return 'btn btn-success'
      case 'warning': return 'btn btn-warning'
      default: return 'btn sample-primary'
    }
  }

  const getSizeClass = () => {
    switch (size) {
      case 'sm': return 'btn-sm'
      case 'lg': return 'btn-lg'
      default: return ''
    }
  }

  return (
    <button
      disabled={disabled || loading}
      className={`${getVariantClass()} ${getSizeClass()} ${loading ? 'btn-loading' : ''}`}
      {...props}
    >
      {loading && <div className="btn-loading-spinner"></div>}
      {children}
    </button>
  )
}
