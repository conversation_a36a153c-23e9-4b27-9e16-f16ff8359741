/* ========================================
   REUSABLE MODAL COMPONENT STYLES
   Organized by component sections for better maintainability
   
   NOTE: This file uses Tailwind CSS @apply directives and vendor-specific
   properties like text-fill-color. Linter warnings for these are expected
   and acceptable in a Tailwind CSS project.
   ======================================== */

/* ========================================
   BACKDROP STYLES
   ======================================== */

/* Mobile sidebar overlay/backdrop */
.sidebar-overlay {
  /* stylelint-disable-next-line at-rule-no-unknown */
  @apply fixed inset-0 z-40 bg-gray-600 bg-opacity-75;
}

.sidebar-overlay.lg-hidden {
  /* stylelint-disable-next-line at-rule-no-unknown */
  @apply lg:hidden;
}

/* General overlay backdrop for modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 40;
}

.modal-overlay.dark {
  @apply bg-black bg-opacity-50;
}

.modal-overlay.light {
  @apply bg-transparent bg-opacity-50;
}

/* Backdrop blur effects */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* ========================================
   CONTAINER STYLES
   ======================================== */

/* Modal container base */
.modal-container {
  @apply fixed bg-white rounded-xl shadow-2xl border border-gray-200 p-0 overflow-hidden cursor-move flex flex-col;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-container.sample-style {
  @apply bg-white rounded-xl border border-gray-200 p-0 overflow-hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  isolation: isolate;
  contain: layout style paint;
  resize: none;
  transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  animation: modalAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalAppear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-container.draggable {
  @apply draggable-modal;
}

.modal-container.no-backdrop {
  /* No backdrop styling - modal is draggable without overlay */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* Modal container with motion/framer-motion support */
.modal-container-motion {
  @apply bg-transparent rounded-xl shadow-2xl border border-gray-200 p-0 overflow-hidden draggable-modal;
}

.modal-container-motion.dragging {
  @apply cursor-grabbing;
}

.modal-container-motion.default {
  @apply cursor-default;
}

/* Modal container positioning */
.modal-positioned {
  @apply fixed;
}

.modal-positioned.dragging {
  @apply cursor-grabbing;
}

.modal-positioned.default {
  @apply cursor-default;
}

/* Modal z-index utilities - removed to prevent overrides */

/* Draggable modal utility class */
.draggable-modal {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Drag handle utility class */
.drag-handle {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Modal dynamic styles (from modal-system.tsx) */
.modal-container-dynamic {
  position: fixed;
}

.modal-container-dragging {
  cursor: grabbing;
}

.modal-container-default {
  cursor: default;
}

/* Modal component specific containers */
.modal-payment-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-project-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-blog-container {
  /* Dynamic top and left will be set via inline styles */
}

/* ========================================
   HEADER STYLES
   ======================================== */

/* Modal header base styles */
.modal-header {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

/* Modal header with motion support and icon colors - matching sample modal */
.modal-header-motion {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  height: 80px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

.modal-header-motion.blue-gradient {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 border-blue-700;
}

.modal-header-motion.green-gradient {
  @apply bg-gradient-to-r from-green-500 to-green-600 border-green-700;
}

.modal-header-motion.purple-gradient {
  @apply bg-gradient-to-r from-purple-500 to-purple-600 border-purple-700;
}

.modal-header-motion.orange-gradient {
  @apply bg-gradient-to-r from-orange-500 to-orange-600 border-orange-700;
}

.modal-header-motion.red-gradient {
  @apply bg-gradient-to-r from-red-500 to-red-600 border-red-700;
}

/* Modal header content and icon styles */
.modal-header-content {
  flex: 1;
}

.modal-header-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-icon svg {
  width: 24px;
  height: 24px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.modal-header-close svg {
  width: 20px;
  height: 20px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-motion-content {
  @apply flex items-center space-x-4;
}

.modal-header-motion-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.modal-header-motion-icon svg {
  width: 24px;
  height: 24px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

.modal-header-motion-text {
  flex: 1;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.modal-header-motion-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.2;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-motion-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.3;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-motion-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-motion-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-header-motion-close svg {
  width: 20px;
  height: 20px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* ========================================
   BODY STYLES
   ======================================== */

/* Modal content base */
.modal-content {
  @apply flex-1 overflow-y-auto cursor-default;
}

/* Custom scrollbar styling for modal content */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Firefox scrollbar styling */
.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

.modal-content-motion {
  @apply flex-1 overflow-y-auto cursor-default;
}

/* Modal form styles */
.modal-form {
  @apply p-4 space-y-4;
}

.modal-form.compact {
  @apply p-3 space-y-3;
}

/* Modal form sections */
.modal-form-section {
  @apply space-y-3;
}

.modal-form-section.compact {
  @apply space-y-2;
}

.modal-form-section-motion {
  background-color: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.modal-form-section-motion.compact {
  @apply p-3;
}

.modal-form-section-motion.default {
  @apply p-4;
}

/* Removed - icons are now directly inside titles */

.modal-form-section-motion-header.compact {
  @apply mb-2;
}

.modal-form-section-motion-header.default {
  @apply mb-3;
}

.modal-form-section-motion-icon {
  @apply w-5 h-5 rounded flex items-center justify-center;
}

.modal-form-section-motion-icon.blue {
  @apply bg-blue-100 text-blue-600;
}

.modal-form-section-motion-icon.green {
  @apply bg-green-100 text-green-600;
}

.modal-form-section-motion-icon.purple {
  @apply bg-purple-100 text-purple-600;
}

.modal-form-section-motion-icon.orange {
  @apply bg-orange-100 text-orange-600;
}

.modal-form-section-motion-icon.red {
  @apply bg-red-100 text-red-600;
}

.modal-form-section-motion-title {
  font-size: 18px !important;
  font-weight: 600;
  color: #1e293b !important;
  margin: 0 0 20px 0 !important;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Modal form fields */
.modal-form-field {
  /* Container for form field */
  display: flex;
  flex-direction: column;
}

.modal-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-form-grid.compact {
  @apply gap-3;
}

.modal-form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.modal-required {
  color: #dc2626;
  font-weight: 600;
}

.modal-form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-input-error {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.modal-form-input-error:focus {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.modal-form-input.with-icon {
  @apply pl-10;
}

.modal-form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
  resize: vertical;
}

.modal-form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea.modal-form-input-error {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.modal-form-select.modal-form-input-error {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.modal-form-checkbox.modal-form-input-error {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.modal-form-checkbox {
  @apply mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded;
}

.modal-form-error {
  @apply mt-1 text-sm text-red-600;
}

.modal-form-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400;
}

/* Modal buttons */
.modal-button {
  @apply inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
}

.modal-button.primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.modal-button.secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.modal-button.outline {
  @apply border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
}

.modal-button.danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.modal-button.success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.modal-button.warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.modal-button.sm {
  @apply px-3 py-1.5 text-sm;
}

.modal-button.md {
  @apply px-4 py-2 text-sm;
}

.modal-button.lg {
  @apply px-6 py-3 text-base;
}

.modal-button-loading-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2;
}

/* Sample modal button styles - using buttons.css classes instead */

/* Modal cards & sections */
.modal-card {
  @apply border rounded-lg p-4;
}

.modal-card.blue-gradient {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200;
}

.modal-card.gray {
  @apply bg-gray-50 border-gray-200;
}

.modal-card.white {
  @apply bg-transparent border-gray-300;
}

.modal-card-header {
  @apply flex items-center justify-between mb-3;
}

.modal-card-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-card-amount {
  @apply text-right;
}

.modal-card-amount-value {
  @apply text-2xl font-bold text-blue-600;
}

.modal-card-amount-label {
  @apply text-sm text-gray-500;
}

.modal-card-content {
  @apply space-y-2 text-sm;
}

.modal-card-row {
  @apply flex justify-between;
}

.modal-card-row-label {
  @apply text-gray-600;
}

.modal-card-row-value {
  @apply font-medium;
}

.modal-card-row.discount {
  @apply text-green-600;
}

.modal-card-row.fee {
  @apply text-gray-500;
}

/* Modal dropdown styles */
.modal-dropdown {
  @apply relative;
}

.modal-dropdown-button {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg bg-transparent text-left focus:ring-2 focus:ring-green-500 focus:border-green-500;
}

.modal-dropdown-content {
  @apply flex items-center justify-between;
}

.modal-dropdown-selection {
  @apply flex items-center;
}

.modal-dropdown-icon {
  @apply h-5 w-5 text-gray-400 mr-3;
}

.modal-dropdown-text {
  /* Container for dropdown text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-title {
  @apply font-medium text-gray-900;
}

.modal-dropdown-description {
  @apply text-sm text-gray-500;
}

.modal-dropdown-arrow {
  @apply h-5 w-5 text-gray-400 transition-transform;
}

.modal-dropdown-arrow.open {
  @apply rotate-180;
}

.modal-dropdown-menu {
  @apply absolute mt-1 w-full bg-transparent border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto;
}

.modal-dropdown-item {
  @apply w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50;
}

.modal-dropdown-item.selected {
  @apply bg-green-50 border-l-4 border-green-500;
}

.modal-dropdown-item-content {
  @apply flex items-center justify-between;
}

.modal-dropdown-item-icon {
  @apply h-5 w-5 text-gray-400 mr-3;
}

.modal-dropdown-item-text {
  /* Container for item text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-item-title {
  @apply font-medium text-gray-900;
}

.modal-dropdown-item-description {
  @apply text-sm text-gray-500;
}

.modal-dropdown-item-fee {
  @apply text-sm text-gray-500;
}

/* Modal status messages */
.modal-message {
  @apply border rounded-lg p-3;
}

.modal-message.success {
  @apply bg-green-50 border-green-200;
}

.modal-message.error {
  @apply bg-red-50 border-red-200;
}

.modal-message.warning {
  @apply bg-yellow-50 border-yellow-200;
}

.modal-message.info {
  @apply bg-blue-50 border-blue-200;
}

.modal-message-content {
  @apply flex items-center;
}

.modal-message-icon {
  @apply h-5 w-5 mr-2;
}

.modal-message-icon.success {
  @apply text-green-500;
}

.modal-message-icon.error {
  @apply text-red-500;
}

.modal-message-icon.warning {
  @apply text-yellow-500;
}

.modal-message-icon.info {
  @apply text-blue-500;
}

.modal-message-text {
  @apply text-sm font-medium;
}

.modal-message-text.success {
  @apply text-green-800;
}

.modal-message-text.error {
  @apply text-red-800;
}

.modal-message-text.warning {
  @apply text-yellow-800;
}

.modal-message-text.info {
  @apply text-blue-800;
}

/* Modal checkbox sections */
.modal-checkbox-section {
  @apply space-y-3;
}

.modal-checkbox-item {
  @apply flex items-start;
}

.modal-checkbox-label {
  @apply ml-3;
}

.modal-checkbox-title {
  @apply text-sm font-medium text-gray-900;
}

.modal-checkbox-description {
  @apply text-sm text-gray-500;
}

.modal-checkbox-input {
  @apply mt-2;
}

/* Modal promo code section */
.modal-promo-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 uppercase;
}

.modal-promo-success {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2;
}

.modal-promo-success-icon {
  @apply h-5 w-5 text-green-500;
}

.modal-promo-message {
  @apply mt-1 text-sm text-green-600;
}

/* Modal security & payment sections */
.modal-security-section {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-4;
}

.modal-security-header {
  @apply flex items-center mb-3;
}

.modal-security-icon {
  @apply h-5 w-5 text-green-600 mr-2;
}

.modal-security-title {
  @apply text-sm font-semibold text-gray-900;
}

.modal-security-status {
  @apply border rounded-lg p-3 mb-3;
}

.modal-security-status.creating {
  @apply bg-blue-50 border-blue-200;
}

.modal-security-status.ready {
  @apply bg-green-50 border-green-200;
}

.modal-security-status-content {
  @apply flex items-center;
}

.modal-security-status-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2;
}

.modal-security-status-icon {
  @apply h-4 w-4 text-green-500 mr-2;
}

.modal-security-status-text {
  @apply text-sm;
}

.modal-security-status-text.creating {
  @apply text-blue-700;
}

.modal-security-status-text.ready {
  @apply text-green-700;
}

.modal-security-card {
  @apply bg-transparent border border-gray-300 rounded-lg p-3;
}

.modal-security-footer {
  @apply text-xs text-gray-500 mt-2 flex items-center;
}

.modal-security-footer-icon {
  @apply h-3 w-3 mr-1;
}

/* Modal resize handle - invisible border handles like sample modal */
.modal-resize-handle {
  position: absolute;
  background: transparent;
}

/* Border resize handles - 4px borders like sample modal */
.modal-resize-handle-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: n-resize;
}

.modal-resize-handle-right {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  cursor: e-resize;
}

.modal-resize-handle-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: s-resize;
}

.modal-resize-handle-left {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  cursor: w-resize;
}

/* Note: All resize handle variants removed - using border handles only like sample modal */

/* Ensure no corner resize handles appear - only border handles */
.modal-container {
  resize: none;
  overflow: hidden;
}

.modal-container * {
  resize: none;
}

/* Prevent any browser default resize handles */
.modal-container,
.modal-container *,
.modal-container::before,
.modal-container::after {
  resize: none;
  overflow: hidden;
}


/* Modal content specific styles */
.modal-project-content {
  height: calc(100% - 80px);
}

.modal-blog-content {
  cursor: default;
}

.modal-blog-form-content {
  /* Dynamic max-height and min-height will be set via inline styles */
  position: relative;
}

.modal-blog-view-mode-text {
  background-color: transparent;
}

/* ========================================
   MODAL FORM SECTIONS
   ======================================== */

.modal-form-section {
  @apply bg-white border border-gray-200 rounded-lg p-3 mb-4;
}

.modal-form-section.sample-style {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 0;
}

.modal-form-section-header.sample-style {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.modal-form-section-title.sample-style {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.modal-form-section-icon.sample-style {
  width: 16px;
  height: 16px;
  color: #3b82f6;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-form-section-icon.sample-style.green {
  color: #10b981;
}

.modal-form-section-icon.sample-style.orange {
  color: #f59e0b;
}

.modal-form-section-header {
  @apply flex items-center space-x-2 mb-3;
}

.modal-form-section-icon {
  @apply h-4 w-4 text-blue-600;
}

.modal-form-section-title {
  @apply font-semibold text-gray-900;
}

.modal-form-section-content {
  @apply space-y-3;
}

.modal-form-section-content.sample-style {
  @apply space-y-3;
}

.modal-form-grid.sample-style {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-form-input.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-select.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-textarea.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.modal-form-textarea.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

/* ========================================
   MODAL CONTENT STRUCTURE
   ======================================== */

.modal-content {
  @apply flex flex-col border border-gray-200 p-0;
}

.modal-content-body {
  @apply flex-1 overflow-y-auto cursor-default;
}

.modal-content.sample-style {
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px);
  padding-bottom: 60px;
  opacity: 1;
  background-color: transparent;
  flex: 1;
}

.modal-content-body.sample-style {
  flex: 1;
  padding: 20px;
  cursor: default;
  overflow-y: auto;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-content-wrapper.sample-style {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-form {
  @apply space-y-3;
}

/* ========================================
   MODAL FOOTER
   ======================================== */

.modal-footer {
  @apply bg-gray-100 p-3;
}

.modal-footer-buttons {
  @apply flex justify-center space-x-3;
}

.modal-footer.sample-style {
  background-color: white;
  padding: 24px 24px 0 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 60px;
  opacity: 1;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.modal-footer.no-padding {
  padding: 6px 8px 0 8px;
}

.modal-footer-buttons.sample-style {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  min-height: 48px;
}

/* Modal status message styles (matching sample modal) */
.modal-status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.modal-status-message svg {
  width: 16px;
  height: 16px;
  color: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* ========================================
   MODAL RESIZE HANDLE
   ======================================== */

/* Note: Modal resize handles are defined above as invisible border handles */

/* ========================================
   MODAL HEADER COMPONENTS
   ======================================== */

.modal-header-icon {
  @apply p-1 bg-transparent/10 rounded-md;
}

.modal-header-icon-svg {
  @apply h-8 w-8 text-transparent;
}

.modal-header-content {
  @apply flex-1;
}

.modal-header-title {
  @apply text-xl font-bold text-white;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-header-subtitle {
  @apply text-sm font-medium text-white/80;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-close-button {
  @apply p-1 hover:bg-white/10 rounded-md transition-colors;
}

.modal-close-icon {
  @apply h-6 w-6 text-white;
}

/* ========================================
   ADMIN/CLIENT DASHBOARD MODAL OVERRIDES
   ======================================== */

.admin-page .modal-container,
.admin-page .modal-container-motion,
.client-page .modal-container,
.client-page .modal-container-motion {
  background-color: #ffffff;
  color: #111827;
  border-color: #e5e7eb;
}

/* Modal form inputs override for admin/client dashboards */
.admin-page .modal-form-input,
.admin-page .modal-form-select,
.admin-page .modal-form-textarea,
.client-page .modal-form-input,
.client-page .modal-form-select,
.client-page .modal-form-textarea {
  background-color: #ffffff;
  color: #111827;
  border-color: #d1d5db;
}

.admin-page .modal-form-input::placeholder,
.admin-page .modal-form-textarea::placeholder,
.client-page .modal-form-input::placeholder,
.client-page .modal-form-textarea::placeholder {
  color: #9ca3af;
}

.modal-theme-blue {
  @apply bg-blue-50 text-blue-900;
}

.modal-theme-green {
  @apply bg-green-50 text-green-900;
}

/* Color utility classes for dynamic styling */
.modal-color-blue {
  @apply text-blue-500;
}

.modal-color-green {
  @apply text-green-500;
}

.modal-color-purple {
  @apply text-purple-500;
}

.modal-color-orange {
  @apply text-orange-500;
}

.modal-color-red {
  @apply text-red-500;
}

/* Required field indicator */
.modal-required {
  @apply text-red-500;
}

/* Resize handle SVG */
.modal-resize-svg {
  @apply w-4 h-4 text-white;
}

/* Modal accessibility */
.modal-focus-trap {
  /* Focus trap styles if needed */
  position: relative;
  outline: none;
}

.modal-sr-only {
  @apply sr-only;
}

.modal-aria-hidden {
  aria-hidden: true;
}

/* ========================================
   FOOTER STYLES
   ======================================== */

/* Modal footer base */
.modal-footer {
  @apply border-t border-gray-200 bg-transparent rounded-b-xl shadow-lg p-4 mt-auto;
}

.modal-footer.centered {
  @apply flex justify-center space-x-3;
}

.modal-footer.left-aligned {
  @apply flex justify-end space-x-3;
}

.modal-footer.compact {
  @apply p-3;
}

.modal-footer-basic {
  @apply border-t border-gray-200 bg-gray-50 p-4;
}

/* ========================================
   TRANSITIONS & ANIMATIONS
   ======================================== */

/* Modal animation utilities */
.modal-fade-in {
  @apply transition-opacity duration-300 ease-in-out;
}

.modal-slide-in {
  @apply transition-transform duration-300 ease-in-out;
}

.modal-scale-in {
  @apply transition-all duration-300 ease-in-out;
}

/* ========================================
   INVOICE MODAL STYLES
   ======================================== */

.invoice-details-container {
  background-color: #e9ecef;
  border: 1px solid #e9ecef;
}

.invoice-details-title {
  color: #495057;
}

.invoice-details-icon {
  color: #6c757d;
}

.invoice-header-desc {
  background-color: #d1d5db;
  color: #374151;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.invoice-header-qty,
.invoice-header-price,
.invoice-header-total {
  background-color: #d1d5db;
  color: #374151;
}

.invoice-header-del {
  background-color: #d1d5db;
  color: #374151;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.invoice-item-row {
  margin: 0;
  padding: 2px 0;
}

.invoice-summary-container {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.invoice-summary-title {
  color: #495057;
}

.invoice-summary-icon {
  color: #6c757d;
}

.invoice-summary-subtotal {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
}

.invoice-summary-subtotal-label {
  color: #1565c0;
}

.invoice-summary-subtotal-value {
  color: #0d47a1;
}

.invoice-summary-tax {
  background-color: #fff3e0;
  border: 1px solid #ffcc02;
}

.invoice-summary-tax-label {
  color: #e65100;
}

.invoice-summary-tax-value {
  color: #bf360c;
}

.invoice-summary-total {
  background-color: #e8f5e8;
  border: 1px solid #c8e6c9;
}

.invoice-summary-total-label {
  color: #2e7d32;
}

.invoice-summary-total-value {
  color: #1b5e20;
}

/* ========================================
   RESPONSIVE UTILITIES
   ======================================== */

@media (max-width: 768px) {
  .modal-form-grid {
    @apply grid-cols-1;
  }
  
  .modal-header-content {
    @apply space-x-2;
  }
  
  .modal-header-icon svg {
    @apply h-6 w-6;
  }
  
  .modal-header-title {
    @apply text-base;
  }
  
  .modal-header-subtitle {
    @apply text-xs;
  }
}
