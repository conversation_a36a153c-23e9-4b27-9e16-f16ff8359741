"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_services_auth_auth-config_ts";
exports.ids = ["_rsc_src_services_auth_auth-config_ts"];
exports.modules = {

/***/ "(rsc)/./src/services/auth/auth-config.ts":
/*!******************************************!*\
  !*** ./src/services/auth/auth-config.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _config_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/prisma */ \"(rsc)/./src/config/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    console.log('Missing credentials');\n                    return null;\n                }\n                try {\n                    console.log('Attempting auth for:', credentials.email);\n                    // Test database connection first\n                    await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$connect();\n                    console.log('Database connected');\n                    const user = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.users.findFirst({\n                        where: {\n                            email: credentials.email.trim().toLowerCase()\n                        }\n                    });\n                    console.log('User lookup result:', user ? {\n                        id: user.id,\n                        email: user.email,\n                        role: user.role\n                    } : 'Not found');\n                    if (!user || !user.password) {\n                        console.log('User not found or no password');\n                        // await AuditLogger.logAuth(\n                        //   'LOGIN_FAILED',\n                        //   undefined,\n                        //   { email: credentials.email, reason: 'User not found or no password' }\n                        // )\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].compare(credentials.password, user.password);\n                    console.log('Password validation result:', isPasswordValid);\n                    if (!isPasswordValid) {\n                        console.log('Invalid password');\n                        // await AuditLogger.logAuth(\n                        //   'LOGIN_FAILED',\n                        //   user.id.toString(),\n                        //   { email: credentials.email, reason: 'Invalid password' }\n                        // )\n                        return null;\n                    }\n                    console.log('Authentication successful');\n                    // await AuditLogger.logAuth(\n                    //   'LOGIN_SUCCESS',\n                    //   user.id.toString(),\n                    //   { email: credentials.email }\n                    // )\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: `${user.firstname} ${user.lastname}`.trim(),\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Auth error details:', {\n                        error: error instanceof Error ? error.message : 'Unknown error',\n                        stack: error instanceof Error ? error.stack : undefined,\n                        email: credentials.email\n                    });\n                    // Try to log the error, but don't fail if audit logging fails\n                    // try {\n                    //   await AuditLogger.logAuth(\n                    //     'LOGIN_FAILED',\n                    //     undefined,\n                    //     { email: credentials.email, error: error instanceof Error ? error.message : 'Unknown error' }\n                    //   )\n                    // } catch (auditError) {\n                    //   console.error('Audit logging failed:', auditError)\n                    // }\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    debug: false,\n    logger: {\n        error (code, metadata) {\n            console.error('NextAuth Error:', {\n                code,\n                metadata\n            });\n        },\n        warn (code) {\n            console.warn('NextAuth Warning:', code);\n        },\n        debug (code, metadata) {\n            if (true) {\n                console.log('NextAuth Debug:', {\n                    code,\n                    metadata\n                });\n            }\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/auth/auth-config.ts\n");

/***/ })

};
;