/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/projects/[projectId]/invoices/route";
exports.ids = ["app/api/admin/projects/[projectId]/invoices/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_projects_projectId_invoices_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/projects/[projectId]/invoices/route.ts */ \"(rsc)/./src/app/api/admin/projects/[projectId]/invoices/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/projects/[projectId]/invoices/route\",\n        pathname: \"/api/admin/projects/[projectId]/invoices\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/projects/[projectId]/invoices/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/api/admin/projects/[projectId]/invoices/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_projects_projectId_invoices_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/projects/[projectId]/invoices/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/projects/[projectId]/invoices/route.ts":
/*!******************************************************************!*\
  !*** ./src/app/api/admin/projects/[projectId]/invoices/route.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _config_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/prisma */ \"(rsc)/./src/config/prisma.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\nasync function GET(request, { params }) {\n    try {\n        const { projectId } = await params;\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const search = searchParams.get('search') || '';\n        const status = searchParams.get('status') || '';\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            projectid: parseInt(projectId)\n        };\n        if (search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    status: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        // Get invoices with related data\n        const [invoices, total] = await Promise.all([\n            _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.invoices.findMany({\n                where,\n                include: {\n                    _count: {\n                        select: {\n                            payments: true\n                        }\n                    },\n                    projects: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    },\n                    clients: {\n                        select: {\n                            id: true,\n                            companyname: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdat: 'desc'\n                },\n                skip,\n                take: limit\n            }),\n            _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.invoices.count({\n                where\n            })\n        ]);\n        // Transform the data to match expected format\n        const transformedInvoices = invoices.map((invoice)=>({\n                id: Number(invoice.id),\n                clientId: Number(invoice.clientid),\n                projectId: Number(invoice.projectid),\n                totalAmount: Number(invoice.totalamount),\n                subtotal: Number(invoice.subtotal),\n                taxRate: Number(invoice.taxrate),\n                taxAmount: Number(invoice.taxamount),\n                status: invoice.status,\n                dueDate: invoice.duedate.toISOString(),\n                description: invoice.description,\n                paidAt: invoice.paidat?.toISOString(),\n                createdAt: invoice.createdat.toISOString(),\n                updatedAt: invoice.updatedat?.toISOString(),\n                _count: invoice._count ? {\n                    payments: Number(invoice._count.payments)\n                } : {\n                    payments: 0\n                },\n                project: invoice.projects ? {\n                    id: Number(invoice.projects.id),\n                    name: invoice.projects.name\n                } : null,\n                client: invoice.clients ? {\n                    id: Number(invoice.clients.id),\n                    companyname: invoice.clients.companyname\n                } : null\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            data: transformedInvoices,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching project invoices:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch invoices',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    try {\n        const { projectId: projectIdStr } = await params;\n        const projectId = parseInt(projectIdStr);\n        const body = await request.json();\n        // Validate required fields for invoice creation\n        const requiredFields = [\n            'totalAmount',\n            'taxRate',\n            'taxAmount',\n            'status',\n            'dueDate'\n        ];\n        for (const field of requiredFields){\n            if (!(field in body)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Get project to get clientId and orderid\n        const project = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.projects.findUnique({\n            where: {\n                id: projectId\n            },\n            select: {\n                clientid: true,\n                orderid: true\n            }\n        });\n        console.log('Found project:', project);\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                error: 'Project not found'\n            }, {\n                status: 404\n            });\n        }\n        // Get the first contract for this project and order\n        const contract = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.contracts.findFirst({\n            where: {\n                projid: projectId,\n                ...project.clientid && {\n                    clientid: project.clientid\n                },\n                orderid: project.orderid\n            },\n            select: {\n                id: true\n            }\n        });\n        console.log('Found contract:', contract);\n        let contractId = contract?.id;\n        // If no contract exists, create a default one (only if project has a client)\n        if (!contract && project.clientid) {\n            try {\n                console.log('Creating default contract for project:', projectId, 'with orderid:', project.orderid);\n                const newContract = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.contracts.create({\n                    data: {\n                        contname: `Default Contract for Project ${projectId}`,\n                        projid: projectId,\n                        clientid: project.clientid,\n                        orderid: project.orderid || 1,\n                        contstatus: 'ACTIVE',\n                        contvalue: body.totalAmount,\n                        contvaluecurr: 'USD',\n                        billingtype: 'One-time'\n                    }\n                });\n                contractId = newContract.id;\n                console.log('Created contract with ID:', contractId);\n            } catch (error) {\n                console.error('Error creating contract:', error);\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    error: 'Failed to create default contract. Please ensure the project has a valid order.'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Ensure project has a client and contract before creating invoice\n        if (!project.clientid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                error: 'Cannot create invoice for project without a client. Please assign a client to the project first.'\n            }, {\n                status: 400\n            });\n        }\n        if (!contractId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                error: 'Cannot create invoice without a contract. Please ensure the project has a valid contract.'\n            }, {\n                status: 400\n            });\n        }\n        // Create the invoice\n        console.log('Creating invoice with data:', {\n            clientid: project.clientid,\n            projectid: projectId,\n            totalamount: body.totalAmount,\n            subtotal: body.subtotal,\n            taxrate: body.taxRate,\n            taxamount: body.taxAmount,\n            status: body.status,\n            duedate: new Date(body.dueDate),\n            description: body.description,\n            paidat: body.paidAt ? new Date(body.paidAt) : null,\n            contid: contractId,\n            orderid: project.orderid || 1\n        });\n        const invoice = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.invoices.create({\n            data: {\n                clientid: project.clientid,\n                projectid: projectId,\n                totalamount: body.totalAmount,\n                subtotal: body.subtotal,\n                taxrate: body.taxRate,\n                taxamount: body.taxAmount,\n                status: body.status,\n                duedate: new Date(body.dueDate),\n                description: body.description,\n                paidat: body.paidAt ? new Date(body.paidAt) : null,\n                contid: contractId,\n                orderid: project.orderid || 1\n            },\n            include: {\n                _count: {\n                    select: {\n                        payments: true\n                    }\n                },\n                projects: {\n                    select: {\n                        id: true,\n                        name: true\n                    }\n                },\n                clients: {\n                    select: {\n                        id: true,\n                        companyname: true\n                    }\n                }\n            }\n        });\n        // Transform the response\n        const transformedInvoice = {\n            id: Number(invoice.id),\n            clientId: Number(invoice.clientid),\n            projectId: Number(invoice.projectid),\n            totalAmount: Number(invoice.totalamount),\n            subtotal: Number(invoice.subtotal),\n            taxRate: Number(invoice.taxrate),\n            taxAmount: Number(invoice.taxamount),\n            status: invoice.status,\n            dueDate: invoice.duedate.toISOString(),\n            description: invoice.description,\n            paidAt: invoice.paidat?.toISOString(),\n            createdAt: invoice.createdat.toISOString(),\n            updatedAt: invoice.updatedat?.toISOString(),\n            _count: invoice._count ? {\n                payments: Number(invoice._count.payments)\n            } : {\n                payments: 0\n            },\n            project: invoice.projects ? {\n                id: Number(invoice.projects.id),\n                name: invoice.projects.name\n            } : null,\n            client: invoice.clients ? {\n                id: Number(invoice.clients.id),\n                companyname: invoice.clients.companyname\n            } : null\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            data: transformedInvoice\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating invoice:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            error: 'Failed to create invoice',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/projects/[projectId]/invoices/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prisma.ts":
/*!******************************!*\
  !*** ./src/config/prisma.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ prisma),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// Re-export for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29uZmlnL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDRCQUE0QjtBQUNMIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvY29uZmlnL3ByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG5cbi8vIFJlLWV4cG9ydCBmb3IgY29udmVuaWVuY2VcbmV4cG9ydCB7IHByaXNtYSBhcyBkYiB9XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2F%5BprojectId%5D%2Finvoices%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();