"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/clients-manager/page",{

/***/ "(app-pages-browser)/./src/components/admin/clients/invoice-modal.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/clients/invoice-modal.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvoiceModal: () => (/* binding */ InvoiceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _styles_components_modals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/components/modals.css */ \"(app-pages-browser)/./src/styles/components/modals.css\");\n/* harmony import */ var _styles_components_forms_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/components/forms.css */ \"(app-pages-browser)/./src/styles/components/forms.css\");\n/* harmony import */ var _styles_components_buttons_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/components/buttons.css */ \"(app-pages-browser)/./src/styles/components/buttons.css\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CurrencyDollarIcon,DocumentTextIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _shared_modal_system__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/modal-system */ \"(app-pages-browser)/./src/components/admin/shared/modal-system.tsx\");\n/* __next_internal_client_entry_do_not_use__ InvoiceModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSubmit, title, initialData, client, project } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [itemsLoading, setItemsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        invoiceNumber: '',\n        dueDate: '',\n        status: 'DRAFT',\n        description: '',\n        taxRate: 0,\n        subtotal: 0,\n        taxAmount: 0,\n        totalAmount: 0,\n        paidAt: ''\n    });\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([\n        {\n            id: 'temp-default',\n            description: '',\n            quantity: 1,\n            unitPrice: 0,\n            totalPrice: 0\n        }\n    ]);\n    // Calculate totals when items or tax rate changes\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const subtotal = items.reduce({\n                \"InvoiceModal.useEffect.subtotal\": (sum, item)=>sum + item.totalPrice\n            }[\"InvoiceModal.useEffect.subtotal\"], 0);\n            const taxAmount = subtotal * formData.taxRate / 100;\n            const totalAmount = subtotal + taxAmount;\n            setFormData({\n                \"InvoiceModal.useEffect\": (prev)=>({\n                        ...prev,\n                        subtotal,\n                        taxAmount,\n                        totalAmount\n                    })\n            }[\"InvoiceModal.useEffect\"]);\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        items,\n        formData.taxRate\n    ]);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"InvoiceModal.useEffect\": ()=>{\n            const loadInvoiceData = {\n                \"InvoiceModal.useEffect.loadInvoiceData\": async ()=>{\n                    if (initialData) {\n                        setFormData({\n                            invoiceNumber: initialData.invoiceNumber || '',\n                            dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',\n                            status: initialData.status || 'DRAFT',\n                            description: initialData.description || '',\n                            taxRate: Number(initialData.taxRate) || 0,\n                            subtotal: Number(initialData.subtotal) || 0,\n                            taxAmount: Number(initialData.taxAmount) || 0,\n                            totalAmount: Number(initialData.totalAmount) || 0,\n                            paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''\n                        });\n                        // Load existing items from API\n                        try {\n                            setItemsLoading(true);\n                            const invoiceId = String(initialData.id);\n                            console.log('Fetching items for invoice ID:', invoiceId);\n                            const response = await fetch(\"/api/admin/invoices/\".concat(invoiceId, \"/items\"));\n                            console.log('Items fetch response status:', response.status, response.statusText);\n                            if (response.ok) {\n                                const result = await response.json();\n                                console.log('Items fetch result:', result);\n                                if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                                    // Parse Decimal.js objects with format {s, e, d}\n                                    const parseDecimal = {\n                                        \"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\": (decimalObj)=>{\n                                            if (!decimalObj || typeof decimalObj !== 'object') return 0;\n                                            const { s, e, d } = decimalObj;\n                                            if (s === undefined || e === undefined || !Array.isArray(d)) return 0;\n                                            // Convert digits array to number\n                                            const digits = d.join('');\n                                            if (!digits) return 0;\n                                            // Calculate the actual value: sign * digits * 10^(exponent - digits.length + 1)\n                                            const value = s * parseFloat(digits) * Math.pow(10, e - digits.length + 1);\n                                            return isNaN(value) ? 0 : value;\n                                        }\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.parseDecimal\"];\n                                    // Map database items to form items\n                                    const mappedItems = result.data.map({\n                                        \"InvoiceModal.useEffect.loadInvoiceData.mappedItems\": (item)=>({\n                                                id: String(item.id),\n                                                description: String(item.description || ''),\n                                                quantity: parseDecimal(item.quantity),\n                                                unitPrice: parseDecimal(item.unitprice),\n                                                totalPrice: parseDecimal(item.totalprice)\n                                            })\n                                    }[\"InvoiceModal.useEffect.loadInvoiceData.mappedItems\"]);\n                                    console.log('Mapped items:', mappedItems);\n                                    setItems(mappedItems);\n                                } else {\n                                    // No items found, use default empty item\n                                    console.log('No items found in response, using default empty item');\n                                    setItems([\n                                        {\n                                            id: 'temp-default',\n                                            description: '',\n                                            quantity: 1,\n                                            unitPrice: 0,\n                                            totalPrice: 0\n                                        }\n                                    ]);\n                                }\n                            } else {\n                                // API error, use default empty item\n                                console.error('Failed to fetch invoice items:', response.status, response.statusText);\n                                setItems([\n                                    {\n                                        id: 'temp-default',\n                                        description: '',\n                                        quantity: 1,\n                                        unitPrice: 0,\n                                        totalPrice: 0\n                                    }\n                                ]);\n                            }\n                        } catch (error) {\n                            // Network or other error, use default empty item\n                            console.error('Error loading invoice items:', error);\n                            setItems([\n                                {\n                                    id: 'temp-default',\n                                    description: '',\n                                    quantity: 1,\n                                    unitPrice: 0,\n                                    totalPrice: 0\n                                }\n                            ]);\n                        } finally{\n                            setItemsLoading(false);\n                        }\n                    } else {\n                        // Reset form for new invoice\n                        setFormData({\n                            invoiceNumber: '',\n                            dueDate: '',\n                            status: 'DRAFT',\n                            description: '',\n                            taxRate: 0,\n                            subtotal: 0,\n                            taxAmount: 0,\n                            totalAmount: 0,\n                            paidAt: ''\n                        });\n                        setItems([\n                            {\n                                id: 'temp-default',\n                                description: '',\n                                quantity: 1,\n                                unitPrice: 0,\n                                totalPrice: 0\n                            }\n                        ]);\n                    }\n                }\n            }[\"InvoiceModal.useEffect.loadInvoiceData\"];\n            if (isOpen) {\n                loadInvoiceData();\n            }\n        }\n    }[\"InvoiceModal.useEffect\"], [\n        initialData,\n        isOpen\n    ]);\n    const addItem = ()=>{\n        setItems([\n            ...items,\n            {\n                id: \"temp-\".concat(Date.now()),\n                description: '',\n                quantity: 1,\n                unitPrice: 0,\n                totalPrice: 0\n            }\n        ]);\n    };\n    const removeItem = (index)=>{\n        if (items.length > 1) {\n            setItems(items.filter((_, i)=>i !== index));\n        }\n    };\n    const updateItem = (index, field, value)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems[index] = {\n            ...updatedItems[index],\n            [field]: value\n        };\n        // Auto-calculate total price when quantity or unit price changes\n        if (field === 'quantity' || field === 'unitPrice') {\n            const quantity = field === 'quantity' ? Number(value) || 0 : updatedItems[index].quantity;\n            const unitPrice = field === 'unitPrice' ? Number(value) || 0 : updatedItems[index].unitPrice;\n            updatedItems[index].totalPrice = quantity * unitPrice;\n        }\n        setItems(updatedItems);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            // Validate items\n            const validItems = items.filter((item)=>item.description.trim() !== '');\n            if (validItems.length === 0) {\n                alert('Please add at least one item to the invoice.');\n                setLoading(false);\n                return;\n            }\n            // Validate that all items have positive whole number quantities and prices\n            const invalidItems = validItems.filter((item)=>{\n                // Handle floating-point precision issues by checking if quantity is close to an integer\n                const roundedQuantity = Math.round(item.quantity);\n                const isCloseToInteger = Math.abs(item.quantity - roundedQuantity) < 0.0001;\n                const isQuantityValid = roundedQuantity > 0 && (Number.isInteger(item.quantity) || isCloseToInteger);\n                const isPriceValid = item.unitPrice >= 0;\n                if (!isQuantityValid || !isPriceValid) {\n                    console.log('Invalid item:', {\n                        description: item.description,\n                        originalQuantity: item.quantity,\n                        roundedQuantity: roundedQuantity,\n                        quantityType: typeof item.quantity,\n                        isInteger: Number.isInteger(item.quantity),\n                        isCloseToInteger: isCloseToInteger,\n                        unitPrice: item.unitPrice,\n                        isQuantityValid,\n                        isPriceValid\n                    });\n                }\n                return !isQuantityValid || !isPriceValid;\n            });\n            if (invalidItems.length > 0) {\n                console.log('Invalid items found:', invalidItems);\n                alert('All items must have positive whole number quantities and non-negative unit prices.');\n                setLoading(false);\n                return;\n            }\n            const submitData = {\n                ...formData,\n                items: validItems.map((item)=>{\n                    var _item_id;\n                    return {\n                        ...item,\n                        // Round quantity to handle floating-point precision issues when submitting\n                        quantity: Math.round(item.quantity),\n                        // Remove temporary IDs for new items\n                        id: ((_item_id = item.id) === null || _item_id === void 0 ? void 0 : _item_id.startsWith('temp-')) ? undefined : item.id\n                    };\n                }),\n                clientId: client.id,\n                projectId: project.id\n            };\n            console.log('Submitting invoice data:', submitData);\n            console.log('Valid items:', validItems);\n            await onSubmit(submitData);\n            onClose();\n        } catch (error) {\n            console.error('Error submitting invoice:', error);\n            alert('Failed to save invoice. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDownloadPDF = async ()=>{\n        try {\n            console.log('Starting PDF download with data:', {\n                formData,\n                client,\n                project\n            });\n            // Try basic HTML-to-PDF approach first\n            let response = await fetch('/api/invoices/generate-pdf-basic', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    invoiceData: {\n                        invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                        dueDate: formData.dueDate,\n                        status: formData.status,\n                        description: formData.description,\n                        taxRate: formData.taxRate,\n                        subtotal: formData.subtotal,\n                        taxAmount: formData.taxAmount,\n                        totalAmount: formData.totalAmount,\n                        paidAt: formData.paidAt,\n                        items: items.filter((item)=>item.description.trim() !== '')\n                    },\n                    client,\n                    project\n                })\n            });\n            console.log('Basic PDF API response status:', response.status);\n            if (response.ok) {\n                // Open the HTML in a new window for printing/saving as PDF\n                const htmlContent = await response.text();\n                const printWindow = window.open('', '_blank');\n                if (printWindow) {\n                    printWindow.document.write(htmlContent);\n                    printWindow.document.close();\n                    printWindow.focus();\n                    // The HTML includes auto-print script, so it will automatically open print dialog\n                    console.log('PDF download completed successfully via HTML');\n                    return;\n                }\n            }\n            // If basic approach fails, try simple PDF generation\n            console.log('Basic PDF failed, trying simple PDF approach...');\n            response = await fetch('/api/invoices/generate-pdf-simple', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    invoiceData: {\n                        invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                        dueDate: formData.dueDate,\n                        status: formData.status,\n                        description: formData.description,\n                        taxRate: formData.taxRate,\n                        subtotal: formData.subtotal,\n                        taxAmount: formData.taxAmount,\n                        totalAmount: formData.totalAmount,\n                        paidAt: formData.paidAt,\n                        items: items.filter((item)=>item.description.trim() !== '')\n                    },\n                    client,\n                    project\n                })\n            });\n            console.log('Simple PDF API response status:', response.status);\n            // If simple PDF fails, try the original Puppeteer approach\n            if (!response.ok) {\n                console.log('Simple PDF failed, trying Puppeteer approach...');\n                response = await fetch('/api/invoices/generate-pdf', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        invoiceData: {\n                            invoiceNumber: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                            dueDate: formData.dueDate,\n                            status: formData.status,\n                            description: formData.description,\n                            taxRate: formData.taxRate,\n                            subtotal: formData.subtotal,\n                            taxAmount: formData.taxAmount,\n                            totalAmount: formData.totalAmount,\n                            paidAt: formData.paidAt,\n                            items: items.filter((item)=>item.description.trim() !== '')\n                        },\n                        client,\n                        project\n                    })\n                });\n                console.log('Puppeteer PDF API response status:', response.status);\n            }\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                console.error('PDF generation failed:', errorData);\n                throw new Error(\"Failed to generate PDF: \".concat(errorData.details || errorData.error || 'Unknown error'));\n            }\n            const blob = await response.blob();\n            console.log('PDF blob created, size:', blob.size);\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"invoice-\".concat(formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''), \".pdf\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('PDF download completed successfully');\n        } catch (error) {\n            console.error('Download error:', error);\n            alert(\"PDF generation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_5__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: title,\n        subtitle: \"\".concat(client.companyName, \" - \").concat(project.name),\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-6 w-6 text-white\"\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n            lineNumber: 419,\n            columnNumber: 13\n        }, void 0),\n        iconColor: \"blue\",\n        disableOverlayClick: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"modal-form\",\n                onClick: (e)=>e.stopPropagation(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"modal-form-section sample-style\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modal-form-section-header sample-style\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"modal-form-section-title sample-style\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"modal-form-section-icon sample-style\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Invoice Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"modal-form-label\",\n                                                            children: \"Invoice Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.invoiceNumber || (initialData ? \"INV-\".concat(String(initialData.id).padStart(4, '0')) : ''),\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    invoiceNumber: e.target.value\n                                                                }),\n                                                            placeholder: \"Auto-generated\",\n                                                            className: \"modal-form-input\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"modal-form-label\",\n                                                            children: [\n                                                                \"Due Date \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"modal-required\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 66\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"date\",\n                                                            required: true,\n                                                            value: formData.dueDate,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    dueDate: e.target.value\n                                                                }),\n                                                            className: \"modal-form-input\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"modal-form-label\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.status,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    status: e.target.value\n                                                                }),\n                                                            className: \"modal-form-select\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"DRAFT\",\n                                                                    children: \"Draft\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"SENT\",\n                                                                    children: \"Sent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"PAID\",\n                                                                    children: \"Paid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"OVERDUE\",\n                                                                    children: \"Overdue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"modal-form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"modal-form-label\",\n                                                            children: \"Tax Rate (%)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            step: \"0.01\",\n                                                            value: formData.taxRate,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    taxRate: parseFloat(e.target.value) || 0\n                                                                }),\n                                                            className: \"modal-form-input\",\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modal-form-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"modal-form-label\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            description: e.target.value\n                                                        }),\n                                                    rows: 2,\n                                                    className: \"modal-form-textarea\",\n                                                    placeholder: \"Invoice description...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"modal-form-section sample-style\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modal-form-section-header sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"modal-form-section-title sample-style\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"modal-form-section-icon sample-style green\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        \"Invoice Items (\",\n                                                        items.filter((item)=>item.description.trim() !== '').length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_modal_system__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    type: \"button\",\n                                                    onClick: addItem,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        \"Add Item\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-200 rounded-lg overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 text-sm font-semibold text-gray-700 bg-gray-100 py-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-5 pl-2 border-r border-gray-300\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center border-r border-gray-300\",\n                                                            children: \"Qty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center border-r border-gray-300\",\n                                                            children: \"Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center border-r border-gray-300\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1 text-center\",\n                                                            children: \"\\xd7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 15\n                                                }, this),\n                                                itemsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center space-x-2 text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Loading...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this) : items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 items-center py-0.5 hover:bg-blue-50 transition-colors \".concat(index < items.length - 1 ? 'border-b border-gray-100' : ''),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-5 border-r border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    required: true,\n                                                                    value: item.description,\n                                                                    onChange: (e)=>updateItem(index, 'description', e.target.value),\n                                                                    className: \"w-full text-sm border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-2 py-2 \".concat(item.description.trim() === '' ? 'text-red-500 placeholder-red-300' : 'text-gray-800'),\n                                                                    placeholder: \"Item description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2 border-r border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    required: true,\n                                                                    min: \"1\",\n                                                                    step: \"1\",\n                                                                    value: item.quantity || '',\n                                                                    onChange: (e)=>{\n                                                                        const value = parseInt(e.target.value) || 0;\n                                                                        updateItem(index, 'quantity', value);\n                                                                    },\n                                                                    className: \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.quantity <= 0 ? 'text-red-500' : 'text-gray-800'),\n                                                                    placeholder: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2 border-r border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    required: true,\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    value: item.unitPrice || '',\n                                                                    onChange: (e)=>{\n                                                                        const value = parseFloat(e.target.value) || 0;\n                                                                        updateItem(index, 'unitPrice', value);\n                                                                    },\n                                                                    className: \"w-full text-sm text-center border-0 bg-transparent focus:bg-white focus:ring-1 focus:ring-blue-500 rounded px-1 py-2 \".concat(item.unitPrice < 0 ? 'text-red-500' : 'text-gray-800'),\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2 border-r border-gray-200 text-sm font-semibold text-gray-700 text-center py-2\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    (item.totalPrice || 0).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-1 flex justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>removeItem(index),\n                                                                    disabled: items.length === 1,\n                                                                    className: \"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors disabled:opacity-30 disabled:cursor-not-allowed\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, item.id || \"item-\".concat(index), true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 13\n                                        }, this),\n                                        items.filter((item)=>item.description.trim() !== '').length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modal-message warning\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-message-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"modal-message-icon warning\",\n                                                        children: \"⚠️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"modal-message-text warning\",\n                                                        children: \"Please add at least one item to the invoice.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, this),\n                                        items.some((item)=>item.description.trim() !== '' && (item.quantity <= 0 || item.unitPrice < 0)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modal-message error\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-message-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"modal-message-icon error\",\n                                                        children: \"❌\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"modal-message-text error\",\n                                                        children: \"All items must have positive whole number quantities and non-negative unit prices.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-form-section sample-style\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-form-section-header sample-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"modal-form-section-title sample-style\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"modal-form-section-icon sample-style orange\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Invoice Summary\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-500 mb-1\",\n                                                        children: \"Subtotal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-gray-800\",\n                                                        children: [\n                                                            \"$\",\n                                                            formData.subtotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-500 mb-1\",\n                                                        children: [\n                                                            \"Tax (\",\n                                                            formData.taxRate,\n                                                            \"%)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-gray-800\",\n                                                        children: [\n                                                            \"$\",\n                                                            formData.taxAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-500 mb-1\",\n                                                        children: \"Total Amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-blue-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            formData.totalAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-500 mb-2\",\n                                                        children: \"Payment Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Amount Paid:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-green-600\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Balance Due:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-red-600\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    (formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0)).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2\",\n                                                        children: formData.totalAmount - ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-green-500 rounded-full mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Paid in Full\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 25\n                                                        }, this) : ((initialData === null || initialData === void 0 ? void 0 : initialData.amountPaid) || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-yellow-500 rounded-full mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Partially Paid\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-red-500 rounded-full mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Unpaid\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, this),\n                                            (initialData === null || initialData === void 0 ? void 0 : initialData.payments) && initialData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-500 mb-2\",\n                                                        children: \"Recent Payments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            initialData.payments.slice(0, 3).map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: new Date(payment.paymentDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                payment.amount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                            lineNumber: 713,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            initialData.payments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 text-center pt-1\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    initialData.payments.length - 3,\n                                                                    \" more payments\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: 'white',\n                    padding: '24px 24px 0 24px',\n                    borderTop: '1px solid #e2e8f0',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'absolute',\n                    bottom: 0,\n                    left: 0,\n                    right: 0,\n                    minHeight: '60px',\n                    opacity: 1,\n                    transform: 'none',\n                    borderBottomLeftRadius: '12px',\n                    borderBottomRightRadius: '12px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px',\n                            color: '#64748b',\n                            fontSize: '14px',\n                            position: 'absolute',\n                            left: '24px',\n                            top: '50%',\n                            transform: 'translateY(-50%)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                style: {\n                                    width: '16px',\n                                    height: '16px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: initialData ? 'Last updated: ' + new Date(initialData.updatedAt || Date.now()).toLocaleDateString() : 'Creating new invoice'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                        lineNumber: 750,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '12px',\n                            position: 'absolute',\n                            left: '50%',\n                            top: '50%',\n                            transform: 'translate(-50%, -50%)',\n                            zIndex: 11\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleDownloadPDF,\n                                style: {\n                                    padding: '16px 24px 12px 24px',\n                                    backgroundColor: '#6b7280',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    cursor: 'pointer',\n                                    fontSize: '14px',\n                                    fontWeight: '600',\n                                    transition: 'all 0.2s ease',\n                                    boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                    transform: 'translateY(-2px)',\n                                    marginRight: '12px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '4px'\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.backgroundColor = '#4b5563';\n                                    e.currentTarget.style.transform = 'translateY(-3px)';\n                                    e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.backgroundColor = '#6b7280';\n                                    e.currentTarget.style.transform = 'translateY(-2px)';\n                                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CurrencyDollarIcon_DocumentTextIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        style: {\n                                            width: '12px',\n                                            height: '12px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View / Print\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onClose,\n                                style: {\n                                    padding: '16px 24px 12px 24px',\n                                    backgroundColor: '#6b7280',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    cursor: 'pointer',\n                                    fontSize: '14px',\n                                    fontWeight: '600',\n                                    transition: 'all 0.2s ease',\n                                    boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',\n                                    transform: 'translateY(-2px)'\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.backgroundColor = '#4b5563';\n                                    e.currentTarget.style.transform = 'translateY(-3px)';\n                                    e.currentTarget.style.boxShadow = '0 4px 8px rgba(107, 114, 128, 0.4)';\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.backgroundColor = '#6b7280';\n                                    e.currentTarget.style.transform = 'translateY(-2px)';\n                                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(107, 114, 128, 0.3)';\n                                },\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                onClick: handleSubmit,\n                                disabled: loading,\n                                style: {\n                                    padding: '12px 24px',\n                                    backgroundColor: '#3b82f6',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    cursor: loading ? 'not-allowed' : 'pointer',\n                                    fontSize: '14px',\n                                    fontWeight: '600',\n                                    transition: 'all 0.2s ease',\n                                    boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                                    transform: 'translateY(-2px)',\n                                    opacity: loading ? 0.6 : 1\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!loading) {\n                                        e.currentTarget.style.backgroundColor = '#2563eb';\n                                        e.currentTarget.style.transform = 'translateY(-3px)';\n                                        e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!loading) {\n                                        e.currentTarget.style.backgroundColor = '#3b82f6';\n                                        e.currentTarget.style.transform = 'translateY(-2px)';\n                                        e.currentTarget.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)';\n                                    }\n                                },\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '12px',\n                                                height: '12px',\n                                                border: '2px solid transparent',\n                                                borderTopColor: 'currentColor',\n                                                borderRadius: '50%',\n                                                animation: 'spin 1s linear infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Processing...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 17\n                                }, this) : initialData ? 'Update Invoice' : 'Create Invoice'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                        lineNumber: 766,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n                lineNumber: 730,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/clients/invoice-modal.tsx\",\n        lineNumber: 414,\n        columnNumber: 5\n    }, this);\n}\n_s(InvoiceModal, \"QoYaLiC4JtJpk2MXo92oaNx5OsU=\");\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/clients/invoice-modal.tsx\n"));

/***/ })

});