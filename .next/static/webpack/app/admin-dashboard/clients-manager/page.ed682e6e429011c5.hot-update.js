"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/clients-manager/page",{

/***/ "(app-pages-browser)/./src/components/admin/shared/modal-system.tsx":
/*!******************************************************!*\
  !*** ./src/components/admin/shared/modal-system.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   FormField: () => (/* binding */ FormField),\n/* harmony export */   FormSection: () => (/* binding */ FormSection),\n/* harmony export */   Input: () => (/* binding */ Input),\n/* harmony export */   Modal: () => (/* binding */ Modal),\n/* harmony export */   ModalContent: () => (/* binding */ ModalContent),\n/* harmony export */   ModalFooter: () => (/* binding */ ModalFooter),\n/* harmony export */   Textarea: () => (/* binding */ Textarea),\n/* harmony export */   useDraggableResizable: () => (/* binding */ useDraggableResizable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _styles_components_modals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/components/modals.css */ \"(app-pages-browser)/./src/styles/components/modals.css\");\n/* harmony import */ var _styles_components_forms_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/components/forms.css */ \"(app-pages-browser)/./src/styles/components/forms.css\");\n/* harmony import */ var _styles_components_buttons_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/components/buttons.css */ \"(app-pages-browser)/./src/styles/components/buttons.css\");\n/* __next_internal_client_entry_do_not_use__ useDraggableResizable,Modal,ModalContent,ModalFooter,FormSection,FormField,Input,Textarea,Button auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Enhanced drag and resize hook (matching sample modal)\nconst useDraggableResizable = (isFormOpen)=>{\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [size, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        width: 1200,\n        height: 600\n    });\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [resizeStart, setResizeStart] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const actualContentRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const headerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const footerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Enhanced mouse move handler (matching sample modal)\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseMove]\": (e)=>{\n            if (isDragging && elementRef.current) {\n                const newX = e.clientX - dragStart.x;\n                const newY = e.clientY - dragStart.y;\n                // Keep modal within viewport bounds\n                const maxX = window.innerWidth - size.width;\n                const maxY = window.innerHeight - size.height;\n                const constrainedX = Math.max(0, Math.min(maxX, newX));\n                const constrainedY = Math.max(0, Math.min(maxY, newY));\n                // Immediate DOM update - no state updates during drag for maximum speed\n                elementRef.current.style.left = \"\".concat(constrainedX, \"px\");\n                elementRef.current.style.top = \"\".concat(constrainedY, \"px\");\n                elementRef.current.style.transform = 'none';\n                elementRef.current.style.transition = 'none';\n            } else if (isResizing && elementRef.current) {\n                const deltaX = e.clientX - resizeStart.x;\n                const deltaY = e.clientY - resizeStart.y;\n                // Get resize direction from the element that initiated the resize\n                const resizeElement = document.querySelector('[data-resize-direction]');\n                const direction = (resizeElement === null || resizeElement === void 0 ? void 0 : resizeElement.getAttribute('data-resize-direction')) || 'se';\n                let newWidth = resizeStart.width;\n                let newHeight = resizeStart.height;\n                // Handle different resize directions\n                if (direction.includes('e')) {\n                    newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width + deltaX));\n                }\n                if (direction.includes('w')) {\n                    newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width - deltaX));\n                }\n                if (direction.includes('s')) {\n                    newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height + deltaY));\n                }\n                if (direction.includes('n')) {\n                    newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height - deltaY));\n                }\n                // Immediate DOM update - no state updates during resize for maximum speed\n                elementRef.current.style.width = \"\".concat(newWidth, \"px\");\n                elementRef.current.style.height = \"\".concat(newHeight, \"px\");\n                elementRef.current.style.transition = 'none';\n            }\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseMove]\"], [\n        isDragging,\n        isResizing,\n        dragStart,\n        resizeStart,\n        size.width,\n        size.height\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseUp]\": ()=>{\n            // Sync state with final DOM position for consistency\n            if (elementRef.current && (isDragging || isResizing)) {\n                const rect = elementRef.current.getBoundingClientRect();\n                setPosition({\n                    x: rect.left,\n                    y: rect.top\n                });\n                setSize({\n                    width: rect.width,\n                    height: rect.height\n                });\n            }\n            // Restore transitions when interaction ends\n            if (elementRef.current) {\n                elementRef.current.style.transition = 'box-shadow 0.2s ease';\n            }\n            // Clean up resize direction attribute\n            const resizeElement = document.querySelector('[data-resize-direction]');\n            if (resizeElement) {\n                resizeElement.removeAttribute('data-resize-direction');\n            }\n            setIsDragging(false);\n            setIsResizing(false);\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseUp]\"], [\n        isDragging,\n        isResizing\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragging(true);\n            setDragStart({\n                x: e.clientX - position.x,\n                y: e.clientY - position.y\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseDown]\"], [\n        position\n    ]);\n    const handleResizeMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"useDraggableResizable.useCallback[handleResizeMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsResizing(true);\n            setResizeStart({\n                x: e.clientX,\n                y: e.clientY,\n                width: size.width,\n                height: size.height\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleResizeMouseDown]\"], [\n        size\n    ]);\n    // Handle border resizing (matching sample modal)\n    const handleBorderResizeMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"useDraggableResizable.useCallback[handleBorderResizeMouseDown]\": (e, direction)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsResizing(true);\n            setResizeStart({\n                x: e.clientX,\n                y: e.clientY,\n                width: size.width,\n                height: size.height\n            });\n            // Store resize direction\n            e.currentTarget.setAttribute('data-resize-direction', direction);\n        }\n    }[\"useDraggableResizable.useCallback[handleBorderResizeMouseDown]\"], [\n        size\n    ]);\n    // Modal positioning and auto-height calculation (matching sample modal)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isFormOpen && \"object\" !== 'undefined') {\n                setIsReady(false); // Hide modal while calculating\n                // Modal dimensions\n                const modalWidth = 1200;\n                const initialHeight = 600;\n                // Calculate center position\n                const viewportWidth = window.innerWidth;\n                const viewportHeight = window.innerHeight;\n                // Center calculation with viewport bounds\n                const left = Math.max(0, (viewportWidth - modalWidth) / 2);\n                const top = Math.max(0, (viewportHeight - initialHeight) / 2);\n                // Ensure modal stays within viewport\n                const finalLeft = Math.min(left, viewportWidth - modalWidth);\n                const finalTop = Math.min(top, viewportHeight - initialHeight);\n                // Set initial size and position\n                setSize({\n                    width: modalWidth,\n                    height: initialHeight\n                });\n                setPosition({\n                    x: finalLeft,\n                    y: finalTop\n                });\n                // Show modal and calculate auto-height\n                setTimeout({\n                    \"useDraggableResizable.useEffect\": ()=>{\n                        setIsReady(true);\n                        // Auto-height calculation after content renders\n                        setTimeout({\n                            \"useDraggableResizable.useEffect\": ()=>{\n                                if (actualContentRef.current && headerRef.current && footerRef.current) {\n                                    // Get the actual content height\n                                    const contentHeight = actualContentRef.current.scrollHeight;\n                                    // Get the actual header height\n                                    const headerHeight = headerRef.current.getBoundingClientRect().height;\n                                    // Get the actual footer height\n                                    const footerHeight = footerRef.current.getBoundingClientRect().height;\n                                    // Calculate total height using actual measurements\n                                    const totalHeight = contentHeight + headerHeight + footerHeight;\n                                    const maxHeight = viewportHeight * 0.95;\n                                    const newHeight = Math.min(maxHeight, totalHeight);\n                                    // Re-center with new height\n                                    const newTop = Math.max(0, (viewportHeight - newHeight) / 2);\n                                    const finalNewTop = Math.min(newTop, viewportHeight - newHeight);\n                                    setSize({\n                                        \"useDraggableResizable.useEffect\": (prev)=>({\n                                                ...prev,\n                                                height: newHeight\n                                            })\n                                    }[\"useDraggableResizable.useEffect\"]);\n                                    setPosition({\n                                        \"useDraggableResizable.useEffect\": (prev)=>({\n                                                ...prev,\n                                                y: finalNewTop\n                                            })\n                                    }[\"useDraggableResizable.useEffect\"]);\n                                }\n                            }\n                        }[\"useDraggableResizable.useEffect\"], 50);\n                    }\n                }[\"useDraggableResizable.useEffect\"], 10);\n            } else {\n                setIsReady(false);\n            }\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isFormOpen\n    ]);\n    // Event listeners for dragging and resizing (matching sample modal)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isDragging || isResizing) {\n                document.addEventListener('mousemove', handleMouseMove, {\n                    passive: true\n                });\n                document.addEventListener('mouseup', handleMouseUp, {\n                    passive: true\n                });\n                document.addEventListener('mouseleave', handleMouseUp, {\n                    passive: true\n                });\n                return ({\n                    \"useDraggableResizable.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                        document.removeEventListener('mouseleave', handleMouseUp);\n                    }\n                })[\"useDraggableResizable.useEffect\"];\n            }\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isDragging,\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            const handleWindowResize = {\n                \"useDraggableResizable.useEffect.handleWindowResize\": ()=>{\n                    if (elementRef.current) {\n                        const rect = elementRef.current.getBoundingClientRect();\n                        const maxX = window.innerWidth - size.width;\n                        const maxY = window.innerHeight - size.height;\n                        if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {\n                            const newX = Math.max(0, Math.min(maxX, position.x));\n                            const newY = Math.max(0, Math.min(maxY, position.y));\n                            setPosition({\n                                x: newX,\n                                y: newY\n                            });\n                        }\n                    }\n                }\n            }[\"useDraggableResizable.useEffect.handleWindowResize\"];\n            window.addEventListener('resize', handleWindowResize);\n            return ({\n                \"useDraggableResizable.useEffect\": ()=>window.removeEventListener('resize', handleWindowResize)\n            })[\"useDraggableResizable.useEffect\"];\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        position,\n        size\n    ]);\n    // Cleanup animation frame on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            return ({\n                \"useDraggableResizable.useEffect\": ()=>{\n                    if (animationFrameRef.current) {\n                        cancelAnimationFrame(animationFrameRef.current);\n                    }\n                }\n            })[\"useDraggableResizable.useEffect\"];\n        }\n    }[\"useDraggableResizable.useEffect\"], []);\n    return {\n        position,\n        size,\n        isReady,\n        isDragging,\n        isResizing,\n        handleMouseDown,\n        handleResizeMouseDown,\n        handleBorderResizeMouseDown,\n        elementRef,\n        contentRef,\n        actualContentRef,\n        headerRef,\n        footerRef\n    };\n};\n_s(useDraggableResizable, \"aTBkC4v7Lyy0/gioQ3CaVA/HToM=\");\nfunction Modal(param) {\n    let { isOpen, onClose, title, subtitle, children, showCloseButton = true, icon, iconColor = 'blue', disableOverlayClick = false } = param;\n    _s1();\n    const { position, size, isReady, isDragging, isResizing, handleMouseDown, handleResizeMouseDown, handleBorderResizeMouseDown, elementRef, contentRef, actualContentRef, headerRef, footerRef } = useDraggableResizable(isOpen);\n    // No click outside handler needed - handled directly on overlay\n    if (!isOpen || !isReady) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"bbc6c9ed233085aa\",\n                children: \"@keyframes modalAppear{0%{opacity:0;transform:scale(.9)translatey(-20px)}100%{opacity:1;transform:scale(1)translatey(0)}}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n                    zIndex: 99998,\n                    backdropFilter: 'blur(2px)'\n                },\n                onClick: disableOverlayClick ? undefined : onClose,\n                className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-overlay\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: elementRef,\n                style: {\n                    position: 'fixed',\n                    zIndex: 99999,\n                    width: \"\".concat(size.width, \"px\"),\n                    height: \"\".concat(size.height, \"px\"),\n                    left: \"\".concat(position.x, \"px\"),\n                    top: \"\".concat(position.y, \"px\"),\n                    transition: isDragging || isResizing ? 'none' : 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)'\n                },\n                onClick: (e)=>e.stopPropagation(),\n                className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-container sample-style \".concat(isDragging ? 'dragging' : 'default', \" \").concat(isResizing ? 'resizing' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: headerRef,\n                        onMouseDown: handleMouseDown,\n                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-header-motion blue-gradient \".concat(isDragging ? 'dragging' : 'default'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-header-motion-icon\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-header-motion-text\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-header-motion-title\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, this),\n                                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-header-motion-subtitle\",\n                                        children: subtitle\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-header-motion-close\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"modal-close-icon\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: contentRef,\n                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-content sample-style\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: actualContentRef,\n                            className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-content-body sample-style\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-content-wrapper sample-style\",\n                                children: react__WEBPACK_IMPORTED_MODULE_2___default().Children.map(children, (child)=>{\n                                    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().isValidElement(child) && child.type === ModalFooter) {\n                                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().cloneElement(child, {\n                                            footerRef\n                                        });\n                                    }\n                                    return child;\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onMouseDown: (e)=>handleBorderResizeMouseDown(e, 'n'),\n                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-resize-handle modal-resize-handle-top\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onMouseDown: (e)=>handleBorderResizeMouseDown(e, 'e'),\n                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-resize-handle modal-resize-handle-right\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onMouseDown: (e)=>handleBorderResizeMouseDown(e, 's'),\n                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-resize-handle modal-resize-handle-bottom\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onMouseDown: (e)=>handleBorderResizeMouseDown(e, 'w'),\n                        className: \"jsx-bbc6c9ed233085aa\" + \" \" + \"modal-resize-handle modal-resize-handle-left\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(Modal, \"p3DUUfTSisiWzHqM8LsW3DLRiTs=\", false, function() {\n    return [\n        useDraggableResizable\n    ];\n});\n_c = Modal;\nfunction ModalContent(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-content sample-style\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"modal-content-body sample-style\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n        lineNumber: 435,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ModalContent;\nfunction ModalFooter(param) {\n    let { children, className = '', footerRef } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: footerRef,\n        className: \"modal-footer sample-style \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"modal-footer-buttons sample-style\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n            lineNumber: 453,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n        lineNumber: 452,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ModalFooter;\nfunction FormSection(param) {\n    let { title, icon, iconColor = 'blue', children, className = '', compact = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-form-section \".concat(compact ? 'compact' : 'default', \" \").concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-form-section-header \".concat(compact ? 'compact' : 'default'),\n                children: [\n                    icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-form-section-motion-icon \".concat(iconColor),\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"modal-form-section-title\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-form-section-content \".concat(compact ? 'compact' : 'default'),\n                children: children\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n        lineNumber: 472,\n        columnNumber: 5\n    }, this);\n}\n_c3 = FormSection;\nfunction FormField(param) {\n    let { label, required = false, error, children, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-form-field \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"modal-form-label\",\n                children: [\n                    label,\n                    \" \",\n                    required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"modal-required\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 30\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                lineNumber: 500,\n                columnNumber: 7\n            }, this),\n            children,\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"modal-form-error\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                lineNumber: 505,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n        lineNumber: 499,\n        columnNumber: 5\n    }, this);\n}\n_c4 = FormField;\nfunction Input(param) {\n    let { error, className = '', ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        className: \"modal-form-input \".concat(error ? 'is-invalid' : '', \" \").concat(className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n        lineNumber: 518,\n        columnNumber: 5\n    }, this);\n}\n_c5 = Input;\nfunction Textarea(param) {\n    let { error, className = '', ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: \"modal-form-textarea \".concat(error ? 'is-invalid' : '', \" \").concat(className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n        lineNumber: 532,\n        columnNumber: 5\n    }, this);\n}\n_c6 = Textarea;\nfunction Button(param) {\n    let { variant = 'primary', size = 'md', loading = false, disabled, children, className = '', ...props } = param;\n    const getVariantClass = ()=>{\n        switch(variant){\n            case 'primary':\n                return 'btn sample-primary';\n            case 'secondary':\n                return 'btn sample-secondary';\n            case 'outline':\n                return 'btn btn-outline';\n            case 'danger':\n                return 'btn btn-danger';\n            case 'success':\n                return 'btn btn-success';\n            case 'warning':\n                return 'btn btn-warning';\n            default:\n                return 'btn sample-primary';\n        }\n    };\n    const getSizeClass = ()=>{\n        switch(size){\n            case 'sm':\n                return 'btn-sm';\n            case 'lg':\n                return 'btn-lg';\n            default:\n                return '';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        disabled: disabled || loading,\n        className: \"\".concat(getVariantClass(), \" \").concat(getSizeClass(), \" \").concat(loading ? 'btn-loading' : ''),\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"btn-loading-spinner\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n                lineNumber: 581,\n                columnNumber: 19\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/shared/modal-system.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, this);\n}\n_c7 = Button;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Modal\");\n$RefreshReg$(_c1, \"ModalContent\");\n$RefreshReg$(_c2, \"ModalFooter\");\n$RefreshReg$(_c3, \"FormSection\");\n$RefreshReg$(_c4, \"FormField\");\n$RefreshReg$(_c5, \"Input\");\n$RefreshReg$(_c6, \"Textarea\");\n$RefreshReg$(_c7, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/shared/modal-system.tsx\n"));

/***/ })

});